#!/bin/bash

echo "🧪 Testing Dashboard Fields Storage with curl..."
echo

# Test data
EMAIL="<EMAIL>"
LEVEL="Entry"

echo "1. Starting assessment..."
START_RESPONSE=$(curl -s -X POST http://localhost:3001/api/math-assessments/start \
  -H "Content-Type: application/json" \
  -d '{
    "level": "'$LEVEL'",
    "email": "'$EMAIL'",
    "studentLevel": "Beginner"
  }')

echo "Start response: $START_RESPONSE"

# Extract assessment ID (simple extraction)
ASSESSMENT_ID=$(echo $START_RESPONSE | grep -o '"assessmentId":"[^"]*"' | cut -d'"' -f4)
echo "Assessment ID: $ASSESSMENT_ID"

if [ -z "$ASSESSMENT_ID" ]; then
  echo "❌ Failed to get assessment ID"
  exit 1
fi

echo
echo "2. Submitting assessment with dashboard fields..."

SUBMIT_RESPONSE=$(curl -s -X POST http://localhost:3001/api/math-assessments/$ASSESSMENT_ID/submit \
  -H "Content-Type: application/json" \
  -d '{
    "answers": [
      {
        "questionId": 1,
        "questionType": "multiple-choice",
        "topic": "arithmetic",
        "studentAnswer": "62",
        "timeSpent": 5000
      },
      {
        "questionId": 2,
        "questionType": "multiple-choice",
        "topic": "arithmetic",
        "studentAnswer": "55",
        "timeSpent": 4000
      }
    ],
    "email": "'$EMAIL'",
    "level": "'$LEVEL'",
    "timeSpent": 600,
    "userData": {
      "firstName": "Test",
      "lastName": "Dashboard",
      "name": "Test Dashboard",
      "studentLevel": "Beginner",
      "userType": "student"
    }
  }')

echo "Submit response: $SUBMIT_RESPONSE"
echo
echo "✅ Test completed! Check server logs for dashboard field storage confirmation."
