<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link rel="stylesheet" type="text/css" href="mathInteractive.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.8/lottie.min.js"></script>
    <!-- Interact.js for drag and drop functionality -->
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
    <title>Mathematics Assessment - Skills Gap Analyzer</title>
</head>

<body class="bg-cover bg-center h-screen w-screen overflow-hidden bg-fixed flex items-center justify-center">
    <!-- Header with progress information -->
    <div id="header" class="fixed top-0 left-0 right-0 p-5 bg-blue-900 flex items-center justify-between h-12 text-white text-sm shadow-md hidden">
        <div class="header-texts flex justify-between w-full items-center">
            <h2 class="text-xs">Mathematics Assessment</h2>
            <h2 class="text-xs">Level: <span id="current-level">Entry</span></h2>
            <h2 class="text-xs">Question <span id="current-question">1</span> of <span id="total-questions">22</span></h2>
            <h2 class="text-xs">Time: <span id="timer-display">30:00</span></h2>
        </div>
    </div>

    <!-- User Information Form -->
    <div id="user-form-container" class="hidden">
        <div class="modern-form-container">
            <div class="form-header">
                <h2>Mathematics Assessment</h2>
                <p>Please provide your information to start your mathematics assessment</p>
            </div>

            <div class="form-content">
                <form id="user-form" class="modern-form">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">Personal Information</h3>

                        <div class="input-grid">
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="first-name"
                                    name="first-name"
                                    required
                                    class="modern-input"
                                    placeholder="First Name"
                                >
                            </div>

                            <div class="input-group">
                                <input
                                    type="text"
                                    id="last-name"
                                    name="last-name"
                                    required
                                    class="modern-input"
                                    placeholder="Last Name"
                                >
                            </div>
                        </div>

                        <div class="input-group">
                            <input
                                type="email"
                                id="email"
                                name="email"
                                required
                                class="modern-input"
                                placeholder="Email Address"
                            >
                            <button type="button" id="check-eligibility-btn" class="eligibility-btn">
                                <span class="btn-text">Check My Eligibility</span>
                                <span class="btn-loading hidden">Checking...</span>
                            </button>
                        </div>
                    </div>

                    <!-- User Progress Section -->
                    <div id="user-progress-section" class="form-section hidden">
                        <div id="progress-summary"></div>
                    </div>

                    <!-- Level Selection Section (Dynamic) -->
                    <div id="level-selection-section" class="form-section hidden">
                        <!-- This will be populated dynamically by JavaScript -->
                    </div>

                    <!-- Student Level -->
                    <div class="form-section">
                        <h3 class="section-title">Student Level</h3>
                        <div class="input-group">
                            <select id="student-level" name="student-level" class="modern-select" required>
                                <option value="">Select your level</option>
                                <option value="adult-learner">Adult Learner</option>
                                <option value="returning-student">Returning Student</option>
                                <option value="school-leaver">School Leaver</option>
                                <option value="career-changer">Career Changer</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <div class="form-footer">
                <div class="form-actions">
                    <button type="submit" form="user-form" id="submit-form" class="modern-submit-btn">
                        <span class="btn-text">Start Mathematics Assessment</span>
                        <span class="btn-icon">→</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mathematics Assessment Container -->
    <div id="math-assessment-container" class="hidden">
        <!-- Assessment Instructions -->
        <div id="assessment-instructions" class="assessment-screen">
            <div class="instruction-container">
                <h2 class="instruction-title">Mathematics Assessment Instructions</h2>
                <div class="instruction-content">
                    <div class="instruction-item">
                        <span class="instruction-icon">📝</span>
                        <p>You will have <span id="time-limit-display">30 minutes</span> to complete <span id="question-count-display">22 questions</span></p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">🎯</span>
                        <p>You need <span id="passing-score-display">24 points</span> to pass this level</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">⏰</span>
                        <p>The timer will start when you click "Begin Assessment"</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">💡</span>
                        <p>Answer all questions to the best of your ability</p>
                    </div>
                </div>
                <button id="begin-assessment-btn" class="modern-submit-btn">
                    <span class="btn-text">Begin Assessment</span>
                    <span class="btn-icon">▶</span>
                </button>
            </div>
        </div>

        <!-- Assessment Questions -->
        <div id="assessment-questions" class="assessment-screen hidden">
            <div class="question-container">
                <div class="question-header">
                    <div class="question-progress">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <span class="progress-text">Question <span id="question-number">1</span> of <span id="total-question-count">22</span></span>
                    </div>
                    <div class="timer-container">
                        <span id="timer">30:00</span>
                    </div>
                </div>

                <div class="question-content">
                    <div class="question-topic">
                        <span id="question-topic">Arithmetic</span>
                    </div>
                    <h3 id="question-text" class="question-text">Loading question...</h3>
                    
                    <!-- Multiple Choice Options -->
                    <div id="multiple-choice-options" class="answer-options hidden">
                        <button class="option-btn" data-option="0"></button>
                        <button class="option-btn" data-option="1"></button>
                        <button class="option-btn" data-option="2"></button>
                        <button class="option-btn" data-option="3"></button>
                    </div>

                    <!-- Numeric Input -->
                    <div id="numeric-input" class="answer-input hidden">
                        <input type="text" id="numeric-answer" class="numeric-input" placeholder="Enter your answer">
                        <p class="input-hint">Enter a number (e.g., 42 or 3.14)</p>
                    </div>

                    <!-- Short Answer Input -->
                    <div id="short-answer-input" class="answer-input hidden">
                        <input type="text" id="short-answer" class="text-input" placeholder="Enter your answer">
                        <p class="input-hint">Enter your mathematical expression</p>
                    </div>

                    <!-- Interactive Question Types -->

                    <!-- Number Line Slider -->
                    <div id="number-line-slider" class="interactive-question hidden">
                        <div class="number-line-container">
                            <div class="number-line-labels" id="number-line-labels"></div>
                            <div class="number-line-track" id="number-line-track">
                                <div class="number-line-handle" id="number-line-handle" tabindex="0" role="slider" aria-label="Number line position"></div>
                            </div>
                            <div class="number-line-value" id="number-line-value">0</div>
                        </div>
                        <p class="input-hint">Drag the handle to select your answer</p>
                    </div>

                    <!-- Drag and Drop Matching -->
                    <div id="drag-drop-matching" class="interactive-question hidden">
                        <div class="matching-container">
                            <div class="draggable-items" id="draggable-items"></div>
                            <div class="drop-zones" id="drop-zones"></div>
                        </div>
                        <div class="interactive-controls">
                            <button id="reset-drag-drop-btn" class="reset-btn" type="button">
                                <span class="btn-icon">↺</span>
                                <span class="btn-text">Reset</span>
                            </button>
                        </div>
                        <p class="input-hint">Drag items to their correct positions</p>
                    </div>

                    <!-- Visual Calculator -->
                    <div id="visual-calculator" class="interactive-question hidden">
                        <div class="calculator-container">
                            <div class="calculator-display">
                                <div class="calculation-steps" id="calculation-steps">
                                    <!-- Calculation steps will be shown here -->
                                </div>
                                <div class="current-display" id="current-display">0</div>
                            </div>
                            <div class="calculator-keypad" id="calculator-keypad">
                                <!-- Calculator buttons will be dynamically generated -->
                            </div>
                            <div class="calculator-controls">
                                <button id="clear-calculator-btn" class="reset-btn" type="button">
                                    <span class="btn-icon">C</span>
                                    <span class="btn-text">Clear</span>
                                </button>
                                <button id="step-calculator-btn" class="step-btn" type="button">
                                    <span class="btn-icon">→</span>
                                    <span class="btn-text">Next Step</span>
                                </button>
                            </div>
                        </div>
                        <p class="input-hint">Use the calculator to solve the problem step by step</p>
                    </div>

                    <!-- Number Bonds -->
                    <div id="number-bonds" class="interactive-question hidden">
                        <!-- Content will be dynamically generated by JavaScript -->
                    </div>

                    <!-- Step-by-step Guided Problems -->
                    <div id="step-by-step-guided" class="interactive-question hidden">
                        <div class="guided-problem-container">
                            <div class="current-step">
                                <h4 id="step-instruction" class="step-instruction">Step 1: Follow the instruction</h4>
                                <div class="step-input-container">
                                    <input type="text" id="step-answer" class="step-input" placeholder="Enter your answer">
                                    <button id="check-step-btn" class="check-btn">Check</button>
                                </div>
                                <div id="step-feedback" class="step-feedback hidden"></div>
                                <div id="step-hint" class="step-hint hidden"></div>
                            </div>
                            <div class="progress-indicator">
                                <span id="current-step-number">1</span> of <span id="total-steps">3</span>
                            </div>
                        </div>
                        <p class="input-hint">Complete each step to solve the problem</p>
                    </div>

                    <!-- Coordinate Plotting -->
                    <div id="coordinate-plotting" class="interactive-question hidden">
                        <div class="coordinate-container">
                            <canvas id="coordinate-canvas" class="interactive-canvas"></canvas>
                            <div class="coordinate-controls">
                                <button id="clear-points-btn" class="reset-btn" type="button">
                                    <span class="btn-icon">✕</span>
                                    <span class="btn-text">Clear Points</span>
                                </button>
                                <div class="coordinate-display">
                                    <span id="current-coordinates">(0, 0)</span>
                                </div>
                            </div>
                        </div>
                        <p class="input-hint">Click on the grid to plot points</p>
                    </div>

                    <!-- Ratio Sliders -->
                    <div id="ratio-sliders" class="interactive-question hidden">
                        <div class="ratio-container">
                            <div id="ratio-sliders-container" class="sliders-container"></div>
                            <div class="ratio-display">
                                <span id="current-ratio">1:1</span>
                            </div>
                            <div class="ratio-controls">
                                <button id="reset-ratio-btn" class="reset-btn" type="button">
                                    <span class="btn-icon">↺</span>
                                    <span class="btn-text">Reset</span>
                                </button>
                            </div>
                        </div>
                        <p class="input-hint">Adjust the sliders to show the correct ratio</p>
                    </div>

                    <!-- Equation Builders -->
                    <div id="equation-builders" class="interactive-question hidden">
                        <div class="equation-builder-container">
                            <div id="available-terms" class="terms-container">
                                <h5>Available Terms:</h5>
                                <div id="terms-list" class="terms-list"></div>
                            </div>
                            <div id="equation-workspace" class="equation-workspace">
                                <h5>Build your equation:</h5>
                                <div id="equation-area" class="equation-area"></div>
                            </div>
                            <div class="equation-controls">
                                <button id="clear-equation-btn" class="reset-btn" type="button">
                                    <span class="btn-icon">✕</span>
                                    <span class="btn-text">Clear</span>
                                </button>
                            </div>
                        </div>
                        <p class="input-hint">Drag terms to build the equation</p>
                    </div>

                    <!-- Pattern Completion -->
                    <div id="pattern-completion" class="interactive-question hidden">
                        <div class="pattern-container">
                            <div class="pattern-display">
                                <canvas id="pattern-canvas" class="interactive-canvas"></canvas>
                            </div>
                            <div class="pattern-input-area">
                                <div class="pattern-options" id="pattern-options">
                                    <!-- Pattern options will be dynamically generated -->
                                </div>
                                <div class="pattern-answer-area" id="pattern-answer-area">
                                    <span class="pattern-prompt">What comes next?</span>
                                    <div class="pattern-answer-slot" id="pattern-answer-slot">
                                        <span class="slot-placeholder">?</span>
                                    </div>
                                </div>
                            </div>
                            <div class="pattern-controls">
                                <button id="clear-pattern-btn" class="reset-btn" type="button">
                                    <span class="btn-icon">✕</span>
                                    <span class="btn-text">Clear</span>
                                </button>
                                <button id="hint-pattern-btn" class="hint-btn" type="button">
                                    <span class="btn-icon">💡</span>
                                    <span class="btn-text">Hint</span>
                                </button>
                            </div>
                        </div>
                        <p class="input-hint">Look at the pattern and select what comes next</p>
                    </div>
                </div>

                <div class="question-actions">
                    <button id="skip-question-btn" class="skip-btn">Skip Question</button>
                    <button id="next-question-btn" class="next-btn" disabled>Next Question</button>
                </div>
            </div>
        </div>

        <!-- Calculator Widget (only visible during GCSE Part 2) -->
        <div id="calculator-widget" class="calculator-widget hidden">
            <!-- Calculator Toggle Button -->
            <button id="calculator-toggle-btn" class="calculator-toggle-btn"
                    aria-label="Open Calculator"
                    title="Open Calculator">
                <svg class="calculator-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="4" y="2" width="16" height="20" rx="2"/>
                    <line x1="8" y1="6" x2="16" y2="6"/>
                    <line x1="8" y1="10" x2="16" y2="10"/>
                    <line x1="8" y1="14" x2="16" y2="14"/>
                    <line x1="8" y1="18" x2="16" y2="18"/>
                    <line x1="12" y1="6" x2="12" y2="18"/>
                </svg>
                <span class="calculator-label">Calculator</span>
            </button>

            <!-- Calculator Modal -->
            <div id="calculator-modal" class="calculator-modal hidden">
                <div class="calculator-container" id="calculator-container">
                    <!-- Fixed Header and Display Section -->
                    <div class="calculator-fixed-section">
                        <!-- Calculator Header -->
                        <div class="calculator-header">
                            <span class="calculator-title">Calculator</span>
                            <button id="calculator-close-btn" class="calculator-close-btn"
                                    aria-label="Close Calculator"
                                    title="Close Calculator">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"/>
                                    <line x1="6" y1="6" x2="18" y2="18"/>
                                </svg>
                            </button>
                        </div>

                        <!-- Calculator Display -->
                        <div class="calculator-display">
                            <div id="calculator-screen" class="calculator-screen"
                                 aria-live="polite"
                                 aria-label="Calculator display">0</div>
                        </div>
                    </div>

                    <!-- Scrollable Button Section -->
                    <div class="calculator-scrollable-section" id="calculator-scrollable">
                        <!-- Scroll Indicator Top -->
                        <div class="scroll-indicator scroll-indicator-top" id="scroll-indicator-top"></div>

                        <!-- Calculator Buttons - Casio Style Layout -->
                        <div class="calculator-buttons casio-layout">
                        <!-- Row 1: Scientific Functions (Top Row) -->
                        <button class="calc-btn casio-function" data-function="sin"
                                aria-label="Sine">sin</button>
                        <button class="calc-btn casio-function" data-function="cos"
                                aria-label="Cosine">cos</button>
                        <button class="calc-btn casio-function" data-function="tan"
                                aria-label="Tangent">tan</button>
                        <button class="calc-btn casio-function" data-function="log"
                                aria-label="Logarithm">log</button>

                        <!-- Row 2: Advanced Functions -->
                        <button class="calc-btn casio-function" data-function="ln"
                                aria-label="Natural logarithm">ln</button>
                        <button class="calc-btn casio-function" data-function="sqrt"
                                aria-label="Square root">√</button>
                        <button class="calc-btn casio-function" data-function="square"
                                aria-label="Square">x²</button>
                        <button class="calc-btn casio-function" data-function="power"
                                aria-label="Power">xʸ</button>

                        <!-- Row 3: Memory and Parentheses -->
                        <button class="calc-btn casio-memory" data-action="memory-clear"
                                aria-label="Memory clear">MC</button>
                        <button class="calc-btn casio-memory" data-action="memory-recall"
                                aria-label="Memory recall">MR</button>
                        <button class="calc-btn casio-function" data-action="parenthesis-open"
                                aria-label="Open parenthesis">(</button>
                        <button class="calc-btn casio-function" data-action="parenthesis-close"
                                aria-label="Close parenthesis">)</button>

                        <!-- Row 4: Memory Add/Subtract and Clear Functions -->
                        <button class="calc-btn casio-memory" data-action="memory-add"
                                aria-label="Memory add">M+</button>
                        <button class="calc-btn casio-memory" data-action="memory-subtract"
                                aria-label="Memory subtract">M−</button>
                        <button class="calc-btn casio-clear" data-action="clear"
                                aria-label="All clear">AC</button>
                        <button class="calc-btn casio-clear" data-action="clear-entry"
                                aria-label="Clear entry">C</button>

                        <!-- Row 5: Numbers 7-9 and Division -->
                        <button class="calc-btn casio-number" data-number="7"
                                aria-label="Seven">7</button>
                        <button class="calc-btn casio-number" data-number="8"
                                aria-label="Eight">8</button>
                        <button class="calc-btn casio-number" data-number="9"
                                aria-label="Nine">9</button>
                        <button class="calc-btn casio-operation" data-operation="/"
                                aria-label="Divide">÷</button>

                        <!-- Row 6: Numbers 4-6 and Multiplication -->
                        <button class="calc-btn casio-number" data-number="4"
                                aria-label="Four">4</button>
                        <button class="calc-btn casio-number" data-number="5"
                                aria-label="Five">5</button>
                        <button class="calc-btn casio-number" data-number="6"
                                aria-label="Six">6</button>
                        <button class="calc-btn casio-operation" data-operation="*"
                                aria-label="Multiply">×</button>

                        <!-- Row 7: Numbers 1-3 and Subtraction -->
                        <button class="calc-btn casio-number" data-number="1"
                                aria-label="One">1</button>
                        <button class="calc-btn casio-number" data-number="2"
                                aria-label="Two">2</button>
                        <button class="calc-btn casio-number" data-number="3"
                                aria-label="Three">3</button>
                        <button class="calc-btn casio-operation" data-operation="-"
                                aria-label="Subtract">−</button>

                        <!-- Row 8: Zero, Decimal, Addition -->
                        <button class="calc-btn casio-number casio-zero" data-number="0"
                                aria-label="Zero">0</button>
                        <button class="calc-btn casio-number" data-action="decimal"
                                aria-label="Decimal point">.</button>
                        <button class="calc-btn casio-operation" data-operation="+"
                                aria-label="Add">+</button>

                        <!-- Row 9: Equals (spans full width) -->
                        <button class="calc-btn casio-equals" data-action="equals"
                                aria-label="Equals">=</button>
                        </div>

                        <!-- Scroll Indicator Bottom -->
                        <div class="scroll-indicator scroll-indicator-bottom" id="scroll-indicator-bottom"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assessment Results -->
        <div id="assessment-results" class="assessment-screen hidden">
            <div class="results-container">
                <div class="results-header">
                    <h2 class="results-title">Assessment Complete!</h2>
                    <div class="results-score">
                        <span class="score-label">Your Score:</span>
                        <span id="final-score" class="score-value">0</span>
                        <span class="score-total">/ <span id="max-score">44</span></span>
                    </div>
                    <div class="result-status">
                        <div id="pass-status" class="status-badge">
                            <span id="status-text">Assessment Complete</span>
                        </div>
                    </div>
                </div>

                <div class="results-content">
                    <div class="feedback-section">
                        <h3>Your Performance</h3>
                        <div id="topic-breakdown" class="topic-breakdown">
                            <!-- Topic performance will be populated here -->
                        </div>
                    </div>

                    <div class="recommendations-section">
                        <h3>Next Steps</h3>
                        <div id="recommendations" class="recommendations">
                            <!-- Recommendations will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="results-actions">
                    <button id="view-detailed-report-btn" class="modern-submit-btn">
                        <span class="btn-text">View Detailed Report</span>
                        <span class="btn-icon">📊</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Screen -->
        <div id="assessment-loading" class="assessment-screen hidden">
            <div class="loading-container">
                <div class="loading-animation">
                    <div class="enhanced-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                </div>
                <h3 class="loading-title">Processing Your Assessment</h3>
                <p class="loading-text">Personalising your assessment...</p>

                <!-- Progress indicator -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-steps">
                        <span class="step active">Preparing questions</span>
                        <span class="step">Customizing difficulty</span>
                        <span class="step">Finalizing assessment</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Report Modal -->
    <div id="detailed-report-modal" class="modal-overlay hidden">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">📊 Detailed Mathematics Assessment Report</h2>
                <button id="close-modal-btn" class="modal-close-btn" aria-label="Close modal">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <div class="modal-content">
                <!-- Assessment Summary -->
                <div class="report-section">
                    <h3 class="section-title">📈 Assessment Summary</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">Level:</span>
                            <span id="modal-level" class="summary-value">Entry</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Score:</span>
                            <span id="modal-score" class="summary-value">20 / 44</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Percentage:</span>
                            <span id="modal-percentage" class="summary-value">45%</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Status:</span>
                            <span id="modal-status" class="summary-value">In Progress</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Time Taken:</span>
                            <span id="modal-time" class="summary-value">25 minutes</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Questions:</span>
                            <span id="modal-questions" class="summary-value">22</span>
                        </div>
                    </div>
                </div>

                <!-- Topic Breakdown -->
                <div class="report-section">
                    <h3 class="section-title">📚 Topic Performance Breakdown</h3>
                    <div id="modal-topic-breakdown" class="topic-breakdown-detailed">
                        <!-- Topic breakdown will be populated here -->
                    </div>
                </div>

                <!-- Feedback Section -->
                <div class="report-section">
                    <h3 class="section-title">💬 Detailed Feedback</h3>
                    <div id="modal-feedback" class="feedback-detailed">
                        <!-- Detailed feedback will be populated here -->
                    </div>
                </div>

                <!-- Strengths -->
                <div class="report-section">
                    <h3 class="section-title">💪 Your Strengths</h3>
                    <div id="modal-strengths" class="strengths-list">
                        <!-- Strengths will be populated here -->
                    </div>
                </div>

                <!-- Areas for Improvement -->
                <div class="report-section">
                    <h3 class="section-title">🎯 Areas for Improvement</h3>
                    <div id="modal-improvements" class="improvements-list">
                        <!-- Improvements will be populated here -->
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="report-section">
                    <h3 class="section-title">🚀 Recommended Next Steps</h3>
                    <div id="modal-recommendations" class="recommendations-detailed">
                        <!-- Recommendations will be populated here -->
                    </div>
                </div>

                <!-- Course Recommendations -->
                <div class="report-section">
                    <h3 class="section-title">📖 Suggested Courses</h3>
                    <div id="modal-courses" class="courses-list">
                        <!-- Course recommendations will be populated here -->
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button id="close-modal-footer-btn" class="modal-action-btn primary">
                    <span class="btn-text">Close</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="validationFunctions.js"></script>
    <script src="interactiveQuestionData.js"></script>
    <script src="mathAssessment.js"></script>
    <script>
        // Initialize the mathematics assessment when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all containers are hidden initially
            document.getElementById('math-assessment-container').classList.add('hidden');
            document.getElementById('header').classList.add('hidden');

            // Show only the user form initially
            document.getElementById('user-form-container').classList.remove('hidden');

            // Initialize the mathematics assessment system
            if (typeof MathAssessment !== 'undefined') {
                window.mathAssessment = new MathAssessment();
                window.mathAssessment.init();
            } else {
                console.error('MathAssessment class not found');
            }
        });
    </script>
</body>
</html>
