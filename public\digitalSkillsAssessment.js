/**
 * Digital Skills Assessment Module
 * Handles the digital skills assessment flow for student users
 */

class DigitalSkillsAssessment {
  constructor() {
    this.currentLevel = 'EntryLevel2';
    this.isSubmitted = false;

    // Assessment data
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.answers = [];
    this.assessmentId = null;

    // Assessment metadata
    this.assessmentStartTime = null;
    this.questionStartTimes = [];

    // Performance tracking
    this.performanceData = {};
    this.interactionLogs = {};
    this.currentQuestionPerformance = null;

    // Level specifications (removed timeLimit properties)
    this.levelSpecs = {
      'EntryLevel2': {
        questionCount: 15,
        passingScore: 12,
        maxScore: 30,
        name: "Computer Skills Beginners"
      },
      'EntryLevel2Plus': {
        questionCount: 18,
        passingScore: 15,
        maxScore: 36,
        name: "Computer Skills Beginners Plus"
      },
      'Level1': {
        questionCount: 20,
        passingScore: 18,
        maxScore: 40,
        name: "Computer Skills for Everyday Life"
      },
      'Level2': {
        questionCount: 22,
        passingScore: 20,
        maxScore: 44,
        name: "Computer Skills for Work"
      },
      'EntryLevel3': {
        questionCount: 16,
        passingScore: 14,
        maxScore: 32,
        name: "Improvers Plus"
      },
      'ICDLLevel2': {
        questionCount: 25,
        passingScore: 22,
        maxScore: 50,
        name: "ICDL Level 2"
      },
      'ICDLLevel3': {
        questionCount: 30,
        passingScore: 26,
        maxScore: 60,
        name: "ICDL Level 3"
      },
      'Level3': {
        questionCount: 30,
        passingScore: 27,
        maxScore: 60,
        name: "Computer Skills for Work Level 2"
      }
    };
  }

  /**
   * Initialize the digital skills assessment
   */
  async init() {
    this.setupEventListeners();
    console.log('Digital skills assessment initialized');

    // Auto-initialize if page is loaded directly
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.autoInit());
    } else {
      this.autoInit();
    }
  }

  /**
   * Auto-initialize if page is loaded directly
   */
  autoInit() {
    // Check if we're on the digital skills assessment page
    if (window.location.pathname.includes('digitalSkillsAssessment.html')) {
      console.log('Auto-initializing digital skills assessment');

      // Clear any problematic stored data for now to debug
      localStorage.removeItem('digitalSkillsUserData');
      console.log('Cleared stored user data for debugging');

      // Always show the form for manual entry during debugging
      console.log('Showing form for manual entry...');
      document.getElementById('user-form-container').classList.remove('hidden');
      this.setupFormSubmission();

      // Provide default level selection for new users
      this.setupDefaultLevelSelection();
    }
  }

  /**
   * Setup default level selection for new users
   */
  setupDefaultLevelSelection() {
    const levelSelectionSection = document.getElementById('level-selection-section');
    if (levelSelectionSection) {
      // Show default level selection without requiring eligibility check
      levelSelectionSection.innerHTML = `
        <h3 class="section-title">Select Assessment Level</h3>
        <p class="section-description">Choose your starting level. If you're unsure, begin with Entry Level 2.</p>
        <div class="radio-group">
          <label class="radio-option">
            <input type="radio" name="assessment-level" value="EntryLevel2" checked>
            <span class="radio-custom"></span>
            <div class="radio-content">
              <h4>Entry Level 2 - Computer Skills Beginners</h4>
              <p>Basic computer parts, mouse/keyboard, simple operations</p>
              <div class="level-details">
                <span class="level-badge">Recommended for beginners</span>
              </div>
            </div>
          </label>
        </div>
        <div class="eligibility-note">
          <p><strong>Note:</strong> You can check your eligibility and see available levels by clicking "Check My Eligibility" above, or proceed directly with Entry Level 2.</p>
        </div>
      `;
      levelSelectionSection.classList.remove('hidden');
    }
  }

  /**
   * Pre-fill form with user data
   */
  prefillForm(userData) {
    if (userData.firstName) {
      const firstNameField = document.getElementById('first-name');
      if (firstNameField) firstNameField.value = userData.firstName;
    }

    if (userData.lastName) {
      const lastNameField = document.getElementById('last-name');
      if (lastNameField) lastNameField.value = userData.lastName;
    }

    if (userData.email) {
      const emailField = document.getElementById('email');
      if (emailField) emailField.value = userData.email;
    }

    if (userData.studentLevel) {
      const studentLevelField = document.getElementById('student-level');
      if (studentLevelField) studentLevelField.value = userData.studentLevel;
    }

    // Store user data for later use
    this.userData = userData;
  }

  /**
   * Set up event listeners for the assessment
   */
  setupEventListeners() {
    // Form submission
    this.setupFormSubmission();

    // Navigation buttons
    const prevButton = document.getElementById('prev-question');
    if (prevButton) {
      prevButton.addEventListener('click', () => this.navigateQuestion(-1));
    }

    const nextButton = document.getElementById('next-question');
    if (nextButton) {
      nextButton.addEventListener('click', () => this.navigateQuestion(1));
    }

    // Submit assessment button
    const submitButton = document.getElementById('submit-assessment');
    if (submitButton) {
      submitButton.addEventListener('click', () => this.confirmSubmit());
    }

    // Results actions
    const viewReportButton = document.getElementById('view-detailed-report');
    if (viewReportButton) {
      viewReportButton.addEventListener('click', () => this.showDetailedReport());
    }

    const retakeButton = document.getElementById('retake-assessment');
    if (retakeButton) {
      retakeButton.addEventListener('click', () => this.retakeAssessment());
    }

    const nextLevelButton = document.getElementById('next-level');
    if (nextLevelButton) {
      nextLevelButton.addEventListener('click', () => this.progressToNextLevel());
    }
  }

  /**
   * Set up form submission handler
   */
  setupFormSubmission() {
    const form = document.getElementById('user-form');
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleFormSubmission();
      });
    }

    // Set up eligibility check button
    const eligibilityBtn = document.getElementById('check-eligibility-btn');
    if (eligibilityBtn) {
      eligibilityBtn.addEventListener('click', () => {
        this.checkUserEligibility();
      });
    }

    // Set up email input to reset progress when changed
    const emailInput = document.getElementById('email');
    if (emailInput) {
      emailInput.addEventListener('input', () => {
        this.resetProgressDisplay();
      });
    }
  }

  /**
   * Check user eligibility and progress
   */
  async checkUserEligibility() {
    const emailInput = document.getElementById('email');
    const email = emailInput.value.trim();

    if (!email) {
      alert('Please enter your email address first');
      emailInput.focus();
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      alert('Please enter a valid email address');
      emailInput.focus();
      return;
    }

    const eligibilityBtn = document.getElementById('check-eligibility-btn');
    const btnText = eligibilityBtn.querySelector('.btn-text');
    const btnLoading = eligibilityBtn.querySelector('.btn-loading');

    try {
      // Show loading state
      eligibilityBtn.disabled = true;
      btnText.classList.add('hidden');
      btnLoading.classList.remove('hidden');

      // Fetch user progress
      const baseUrl = window.location.hostname === 'localhost' ? 'http://localhost:3003' : '';
      const response = await fetch(`${baseUrl}/api/digital-skills-progress/${encodeURIComponent(email)}`);

      if (!response.ok) {
        // Handle different HTTP error statuses more gracefully
        if (response.status === 404) {
          // User not found - treat as new user
          console.log('User not found, treating as new user');
          this.displayUserProgress({
            isNewUser: true,
            message: 'Welcome! You can start with Entry Level 2 - Computer Skills Beginners',
            availableLevels: ['EntryLevel2'],
            progressData: {},
            completedLevels: [],
            failedLevels: [],
            statistics: { totalCompleted: 0, totalScore: 0, totalTimeSpent: 0 },
            levelSpecs: this.levelSpecs
          });
          return;
        } else if (response.status >= 500) {
          // Server error
          throw new Error(`Server error: ${response.status}`);
        } else {
          // Other client errors
          throw new Error(`Request failed: ${response.status}`);
        }
      }

      const progressData = await response.json();
      console.log('User progress data:', progressData);

      // Handle server response that indicates an error
      if (progressData.error) {
        throw new Error(progressData.error);
      }

      // Display progress (works for both new and existing users)
      this.displayUserProgress(progressData);

    } catch (error) {
      console.error('Error checking user eligibility:', error);

      // More specific error handling
      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        alert('Unable to connect to the server. Please check your internet connection and try again.');
      } else if (error.message.includes('Server error')) {
        alert('Server is temporarily unavailable. Please try again in a few moments.');
      } else {
        // For any other errors, provide a fallback for new users
        console.log('Providing fallback for new user due to error:', error.message);
        this.displayUserProgress({
          isNewUser: true,
          message: 'Welcome! You can start with Entry Level 2 - Computer Skills Beginners',
          availableLevels: ['EntryLevel2'],
          progressData: {},
          completedLevels: [],
          failedLevels: [],
          statistics: { totalCompleted: 0, totalScore: 0, totalTimeSpent: 0 },
          levelSpecs: this.levelSpecs
        });
      }
    } finally {
      // Reset button state
      eligibilityBtn.disabled = false;
      btnText.classList.remove('hidden');
      btnLoading.classList.add('hidden');
    }
  }

  /**
   * Display user progress and available levels
   */
  displayUserProgress(progressData) {
    const progressSection = document.getElementById('user-progress-section');
    const levelSelectionSection = document.getElementById('level-selection-section');
    const progressSummary = document.getElementById('progress-summary');

    // Show progress section
    progressSection.classList.remove('hidden');

    if (progressData.isNewUser) {
      // Show new user message
      progressSummary.innerHTML = `
        <div class="new-user-message">
          <h4>Welcome to Digital Skills Assessment!</h4>
          <p>You're starting fresh. Begin with Entry Level 2 - Computer Skills Beginners to assess your basic digital skills.</p>
        </div>
      `;
    } else {
      // Show existing user progress
      const stats = progressData.statistics;
      const levels = progressData.levelSpecs;

      progressSummary.innerHTML = `
        <div class="progress-stats-grid">
          <div class="progress-stat">
            <span class="progress-stat-number">${stats.totalCompleted}</span>
            <span class="progress-stat-label">Levels Completed</span>
          </div>
          <div class="progress-stat">
            <span class="progress-stat-number">${stats.totalScore}</span>
            <span class="progress-stat-label">Total Score</span>
          </div>
          <div class="progress-stat">
            <span class="progress-stat-number">${this.formatTimeDuration(stats.totalTimeSpent)}</span>
            <span class="progress-stat-label">Time Spent</span>
          </div>
        </div>
        <div class="progress-levels">
          <h4 style="margin: 0 0 12px 0; color: #1e293b;">Your Progress:</h4>
          ${Object.keys(levels).map(levelKey => {
            const levelData = progressData.progressData[levelKey];
            const spec = levels[levelKey];
            const status = this.getLevelStatusFromData(levelKey, progressData);

            return `
              <div class="progress-level-item">
                <div class="progress-level-info">
                  <div class="progress-level-icon ${status}">
                    ${status === 'completed' ? '✓' : status === 'failed' ? '✗' : status === 'available' ? '▶' : '🔒'}
                  </div>
                  <div class="progress-level-details">
                    <h4>${spec.name}</h4>
                    <p>${spec.description}</p>
                    ${levelData && levelData.completed ? `<p>Score: ${levelData.score}/${spec.maxScore} (${Math.round((levelData.score/spec.maxScore)*100)}%)</p>` : ''}
                  </div>
                </div>
                <div class="progress-level-status ${status}">
                  ${status === 'completed' ? 'Completed' : status === 'failed' ? 'Failed - Retry Available' : status === 'available' ? 'Available' : 'Locked'}
                </div>
              </div>
            `;
          }).join('')}
        </div>
      `;
    }

    // Show level selection with available levels
    this.showAvailableLevels(progressData.availableLevels, progressData.levelSpecs);
  }

  /**
   * Show available levels for selection
   */
  showAvailableLevels(availableLevels, levelSpecs) {
    const levelSelectionSection = document.getElementById('level-selection-section');

    if (availableLevels.length === 0) {
      levelSelectionSection.innerHTML = `
        <h3 class="section-title">Assessment Complete</h3>
        <div class="new-user-message">
          <h4>Congratulations!</h4>
          <p>You have completed all available digital skills levels. Well done!</p>
        </div>
      `;
    } else {
      levelSelectionSection.innerHTML = `
        <h3 class="section-title">Available Assessments</h3>
        <p class="section-description">Select a level to begin or continue your assessment</p>
        <div class="radio-group">
          ${availableLevels.map((levelKey, index) => {
            const spec = levelSpecs[levelKey];
            return `
              <label class="radio-option">
                <input type="radio" name="assessment-level" value="${levelKey}" ${index === 0 ? 'checked' : ''}>
                <span class="radio-custom"></span>
                <div class="radio-content">
                  <span class="radio-title">${spec.name}</span>
                  <span class="radio-description">${spec.description} (${spec.maxScore/2} min, ${spec.maxScore/2} questions)</span>
                </div>
              </label>
            `;
          }).join('')}
        </div>
      `;
    }

    levelSelectionSection.classList.remove('hidden');
  }

  /**
   * Get level status from progress data
   */
  getLevelStatusFromData(levelKey, progressData) {
    const levelData = progressData.progressData[levelKey];
    const isAvailable = progressData.availableLevels.includes(levelKey);

    if (levelData && levelData.completed) {
      return levelData.passed ? 'completed' : 'failed';
    } else if (isAvailable) {
      return 'available';
    } else {
      return 'locked';
    }
  }

  /**
   * Reset progress display
   */
  resetProgressDisplay() {
    const progressSection = document.getElementById('user-progress-section');
    const levelSelectionSection = document.getElementById('level-selection-section');

    progressSection.classList.add('hidden');
    levelSelectionSection.classList.add('hidden');
  }

  /**
   * Format time duration in milliseconds to readable format
   */
  formatTimeDuration(milliseconds) {
    if (!milliseconds || milliseconds === 0) return '0m';

    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return '< 1m';
    }
  }

  /**
   * Handle form submission
   */
  async handleFormSubmission() {
    try {
      console.log('Form submission started...');

      // Get form data
      const firstName = document.getElementById('first-name').value;
      const lastName = document.getElementById('last-name').value;
      const email = document.getElementById('email').value;
      const studentLevel = document.getElementById('student-level').value;

      console.log('Form data:', { firstName, lastName, email, studentLevel });
      
      // Get selected level
      const levelRadios = document.getElementsByName('assessment-level');
      let selectedLevel = 'EntryLevel2'; // Default

      for (const radio of levelRadios) {
        if (radio.checked) {
          selectedLevel = radio.value;
          break;
        }
      }

      console.log('Selected level:', selectedLevel);
      console.log('Available levelSpecs:', Object.keys(this.levelSpecs));

      // Validate form data
      if (!firstName || !lastName || !email || !studentLevel) {
        console.log('Form validation failed - missing fields');
        alert('Please fill in all required fields');
        return;
      }
      
      // Basic email validation
      if (!this.validateEmail(email)) {
        console.log('Email validation failed');
        alert('Please enter a valid email address');
        return;
      }

      // Store user data
      this.userData = {
        firstName,
        lastName,
        name: `${firstName} ${lastName}`,
        email,
        studentLevel,
        userType: 'student'
      };

      console.log('User data stored:', this.userData);

      // Validate selected level exists in levelSpecs
      if (!this.levelSpecs[selectedLevel]) {
        console.error('Invalid level selected:', selectedLevel);
        selectedLevel = 'EntryLevel2'; // Fallback to default
      }

      // Set current level
      this.currentLevel = selectedLevel;
      console.log('Current level set to:', this.currentLevel);

      // Hide form and start assessment directly (skip instructions)
      console.log('Hiding form container and starting assessment...');
      this.hideContainerWithTransition('user-form-container', () => {
        console.log('Form container hidden, showing header and assessment container...');
        document.getElementById('header').classList.remove('hidden');
        document.getElementById('digital-skills-assessment-container').classList.remove('hidden');
        this.startAssessment();
      });
      
    } catch (error) {
      console.error('Error handling form submission:', error);
      alert('An error occurred. Please try again.');
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  }



  /**
   * Hide all assessment screens
   */
  hideAllAssessmentScreens() {
    const screens = document.querySelectorAll('.assessment-screen');
    screens.forEach(screen => {
      screen.classList.add('hidden');
    });
  }

  /**
   * Hide container with enhanced transition
   */
  hideContainerWithTransition(containerId, callback) {
    console.log('hideContainerWithTransition called for:', containerId);
    const container = document.getElementById(containerId);
    console.log('Container found:', container ? 'yes' : 'no');

    if (container) {
      // Add fade-out class for smooth animation
      container.classList.add('fade-out');
      console.log('Added fade-out class, waiting 400ms...');

      setTimeout(() => {
        container.classList.add('hidden');
        container.classList.remove('fade-out');
        console.log('Container hidden, executing callback...');

        if (callback) {
          callback();
        }
      }, 400); // Match CSS transition duration
    } else {
      console.log('Container not found, executing callback immediately...');
      if (callback) {
        callback();
      }
    }
  }

  /**
   * Show container with enhanced transition
   */
  showContainerWithTransition(containerId, displayType = 'flex') {
    const container = document.getElementById(containerId);
    if (container) {
      // Prepare for fade-in animation
      container.style.display = displayType;
      container.classList.remove('hidden');
      container.classList.add('fade-in');

      // Force reflow to ensure the animation plays
      container.offsetHeight;

      // Remove fade-in class after animation completes
      setTimeout(() => {
        container.classList.remove('fade-in');
      }, 400);
    }
  }



  /**
   * Start the assessment by fetching questions
   */
  async startAssessment() {
    try {
      console.log('Starting assessment with userData:', this.userData);
      console.log('Current level:', this.currentLevel);

      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      // Show enhanced loading screen
      console.log('Showing loading screen...');
      this.showProgressiveLoading('Personalising your digital skills assessment...');

      const response = await fetch(`${baseUrl}/api/digital-skills-assessments/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: this.currentLevel,
          email: this.userData.email,
          studentLevel: this.userData.studentLevel
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      this.questions = data.questions;
      this.assessmentId = data.assessmentId;

      console.log('Assessment data received:', {
        questionsCount: this.questions.length,
        assessmentId: this.assessmentId,
        firstQuestion: this.questions[0]
      });

      // Initialize answers array
      this.answers = new Array(this.questions.length).fill(null);

      // Initialize performance tracking
      this.assessmentStartTime = Date.now();
      this.questionStartTimes = new Array(this.questions.length).fill(null);

      // Allow loading animation to complete before showing questions
      setTimeout(() => {
        this.hideLoading();
        this.showQuestions();
      }, 2000); // Give time for the loading animation to complete

    } catch (error) {
      console.error('Error starting digital skills assessment:', error);
      this.hideLoading();
      alert('Failed to start assessment. Please try again.');
    }
  }

  /**
   * Show enhanced loading screen with progressive messages
   */
  showProgressiveLoading(initialMessage) {
    const messages = [
      'Personalising your digital skills assessment...',
      'Analyzing your selected level...',
      'Preparing customized questions...',
      'Optimizing difficulty settings...',
      'Finalizing your assessment...'
    ];

    let currentMessageIndex = 0;

    // Show initial loading
    this.showLoading(messages[currentMessageIndex]);

    // Change messages progressively
    this.loadingMessageInterval = setInterval(() => {
      currentMessageIndex++;
      if (currentMessageIndex < messages.length) {
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
          // Fade out current message
          loadingText.style.opacity = '0.5';

          setTimeout(() => {
            loadingText.textContent = messages[currentMessageIndex];
            loadingText.style.opacity = '1';
          }, 300);
        }
      } else {
        clearInterval(this.loadingMessageInterval);
      }
    }, 1200); // Change message every 1.2 seconds
  }

  /**
   * Show loading screen with enhanced animations
   */
  showLoading(message) {
    console.log('showLoading called with message:', message);

    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show loading screen
    const loadingScreen = document.getElementById('assessment-loading');
    console.log('Loading screen element:', loadingScreen);

    if (loadingScreen) {
      loadingScreen.classList.remove('hidden');
      console.log('Loading screen classes after showing:', loadingScreen.className);
    }

    const loadingText = document.querySelector('.loading-text');
    if (loadingText) {
      loadingText.textContent = message;
    }

    // Add loading animation to progress bar
    const loadingProgressFill = document.querySelector('#assessment-loading .progress-fill');
    if (loadingProgressFill) {
      loadingProgressFill.classList.add('loading-animation');
    }

    // Start progress animation
    this.animateProgressSteps();
  }

  /**
   * Animate progress steps during loading
   */
  animateProgressSteps() {
    const steps = document.querySelectorAll('.step');
    if (steps.length === 0) return;

    // Reset all steps
    steps.forEach(step => {
      step.classList.remove('active', 'completed');
    });

    // Animate steps progressively
    let currentStep = 0;
    const stepInterval = setInterval(() => {
      if (currentStep < steps.length) {
        // Mark previous step as completed
        if (currentStep > 0) {
          steps[currentStep - 1].classList.remove('active');
          steps[currentStep - 1].classList.add('completed');
        }

        // Mark current step as active
        steps[currentStep].classList.add('active');
        currentStep++;
      } else {
        clearInterval(stepInterval);
        // Mark last step as completed
        if (steps.length > 0) {
          steps[steps.length - 1].classList.remove('active');
          steps[steps.length - 1].classList.add('completed');
        }
      }
    }, 1000);

    this.stepAnimationInterval = stepInterval;
  }

  /**
   * Hide loading screen
   */
  hideLoading() {
    // Clear intervals
    if (this.loadingMessageInterval) {
      clearInterval(this.loadingMessageInterval);
      this.loadingMessageInterval = null;
    }
    if (this.stepAnimationInterval) {
      clearInterval(this.stepAnimationInterval);
      this.stepAnimationInterval = null;
    }

    // Remove loading animation from progress bar
    const loadingProgressFill = document.querySelector('#assessment-loading .progress-fill');
    if (loadingProgressFill) {
      loadingProgressFill.classList.remove('loading-animation');
    }

    // Hide loading screen
    const loadingScreen = document.getElementById('assessment-loading');
    if (loadingScreen) {
      loadingScreen.classList.add('hidden');
    }
  }

  /**
   * Update loading progress indicator
   */
  updateLoadingProgress(percentage, message) {
    const progressFill = document.querySelector('#assessment-loading .progress-fill');
    const loadingText = document.querySelector('.loading-text');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
      progressFill.classList.remove('loading-animation'); // Remove the infinite animation class
    }

    if (loadingText && message) {
      loadingText.style.opacity = '0.5';
      setTimeout(() => {
        loadingText.textContent = message;
        loadingText.style.opacity = '1';
      }, 150);
    }
  }

  /**
   * Show the questions interface
   */
  showQuestions() {
    console.log('Showing questions interface, questions available:', this.questions.length);

    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show only questions container
    document.getElementById('assessment-questions').classList.remove('hidden');

    // Ensure assessment progress bar doesn't have loading animation
    const assessmentProgressFill = document.querySelector('#assessment-questions .progress-fill');
    if (assessmentProgressFill) {
      assessmentProgressFill.classList.remove('loading-animation');
      assessmentProgressFill.style.animation = 'none';
    }

    // Load first question
    this.loadCurrentQuestion();
  }

  /**
   * Load the current question
   */
  loadCurrentQuestion() {
    if (this.currentQuestionIndex >= this.questions.length) {
      this.completeAssessment();
      return;
    }

    const question = this.questions[this.currentQuestionIndex];
    console.log('Loading question:', this.currentQuestionIndex + 1, 'of', this.questions.length, question);

    // Record question start time
    if (!this.questionStartTimes[this.currentQuestionIndex]) {
      this.questionStartTimes[this.currentQuestionIndex] = Date.now();
    }

    // Update question display
    document.getElementById('question-title').textContent = question.question;

    // Update progress
    const progressText = document.getElementById('progress-text');
    const progressFill = document.getElementById('progress-fill');
    const currentQuestion = document.getElementById('current-question');

    if (progressText) {
      progressText.textContent = `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
    }

    if (progressFill) {
      const percentage = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
      progressFill.style.width = `${percentage}%`;
      // Ensure no animation classes are present
      progressFill.classList.remove('loading-animation');
      progressFill.style.animation = 'none';
    }

    if (currentQuestion) {
      currentQuestion.textContent = this.currentQuestionIndex + 1;
    }

    // Create options
    this.createQuestionOptions(question);

    // Update navigation buttons
    this.updateNavigationButtons();
  }

  /**
   * Create question options based on question type
   */
  createQuestionOptions(question) {
    const optionsContainer = document.getElementById('question-options');
    if (!optionsContainer) return;

    optionsContainer.innerHTML = '';

    // Handle different question types
    if (question.type === 'self-assessment-rating' || question.type === 'self-assessment-slider' ||
        question.type === 'self-assessment-stars' || question.type === 'self-assessment-practical') {
      this.createSelfAssessmentQuestion(question, optionsContainer);
    } else if (question.type === 'self-assessment-descriptive') {
      this.createDescriptiveSelfAssessmentQuestion(question, optionsContainer);
    } else {
      // Default multiple-choice questions
      this.createMultipleChoiceQuestion(question, optionsContainer);
    }
  }

  /**
   * Create traditional multiple-choice question
   */
  createMultipleChoiceQuestion(question, container) {
    question.options.forEach((option, index) => {
      const optionElement = document.createElement('div');
      optionElement.className = 'question-option';

      const optionId = `option-${this.currentQuestionIndex}-${index}`;

      optionElement.innerHTML = `
        <input type="radio" id="${optionId}" name="question-${this.currentQuestionIndex}" value="${option}">
        <label for="${optionId}" class="option-label">
          <span class="option-letter">${String.fromCharCode(65 + index)}</span>
          <span class="option-text">${option}</span>
        </label>
      `;

      container.appendChild(optionElement);

      // Add event listener
      const radioInput = optionElement.querySelector('input[type="radio"]');
      radioInput.addEventListener('change', () => {
        this.handleAnswerSelection(option);
      });
    });

    // Restore previous answer if exists
    const previousAnswer = this.answers[this.currentQuestionIndex];
    if (previousAnswer) {
      const radioInput = container.querySelector(`input[value="${previousAnswer}"]`);
      if (radioInput) {
        radioInput.checked = true;
      }
    }
  }

  /**
   * Create self-assessment question with competency-based options
   */
  createSelfAssessmentQuestion(question, container) {
    const selfAssessmentDiv = document.createElement('div');
    selfAssessmentDiv.className = 'self-assessment-question';

    // Use the actual options from the question (competency-based)
    const questionOptions = question.options || [
      "I can perform this task independently",
      "I can perform this task with some guidance",
      "I cannot perform this task",
      "I have never attempted this task"
    ];

    selfAssessmentDiv.innerHTML = `
      <div class="practical-assessment">
        <div class="practical-options" id="practical-options-${this.currentQuestionIndex}">
          ${questionOptions.map((option, index) => `
            <label class="practical-option">
              <input type="radio" name="practical-${this.currentQuestionIndex}" value="${index + 1}" data-option="${option}">
              <span class="option-text">${option}</span>
              <div class="selection-indicator">✓</div>
            </label>
          `).join('')}
        </div>
      </div>
    `;

    container.appendChild(selfAssessmentDiv);
    this.setupSelfAssessmentListeners(question);
  }

  /**
   * Create descriptive self-assessment question with confidence-based text options
   */
  createDescriptiveSelfAssessmentQuestion(question, container) {
    const descriptiveDiv = document.createElement('div');
    descriptiveDiv.className = 'descriptive-self-assessment-question';

    // Create header
    const headerDiv = document.createElement('div');
    headerDiv.className = 'descriptive-assessment-header';
    headerDiv.innerHTML = `
      <div class="confidence-prompt">
        <span class="confidence-icon">🎯</span>
        <span class="confidence-text">Select the option that best describes your confidence level:</span>
      </div>
    `;
    descriptiveDiv.appendChild(headerDiv);

    // Create options container
    const optionsDiv = document.createElement('div');
    optionsDiv.className = 'descriptive-options-container';

    // Use provided options or generate default confidence levels
    const options = question.options || [
      "I can do this confidently and independently",
      "I can do this with some guidance or practice",
      "I need significant help to do this",
      "I have never done this before"
    ];

    options.forEach((option, index) => {
      const optionElement = document.createElement('div');
      optionElement.className = 'descriptive-option';

      const optionId = `descriptive-option-${this.currentQuestionIndex}-${index}`;
      const confidenceLevel = this.getConfidenceLevelClass(index, options.length);

      optionElement.innerHTML = `
        <input type="radio" id="${optionId}" name="descriptive-question-${this.currentQuestionIndex}" value="${option}">
        <label for="${optionId}" class="descriptive-option-label ${confidenceLevel}">
          <div class="confidence-indicator-dot"></div>
          <div class="option-content">
            <span class="confidence-level">${this.getConfidenceLevelText(index, options.length)}</span>
            <span class="option-description">${option}</span>
          </div>
          <div class="selection-indicator">✓</div>
        </label>
      `;

      optionsDiv.appendChild(optionElement);

      // Add event listener
      const radioInput = optionElement.querySelector('input[type="radio"]');
      radioInput.addEventListener('change', () => {
        this.handleAnswerSelection(option);
        this.updateDescriptiveOptionStyles(optionsDiv);
      });
    });

    descriptiveDiv.appendChild(optionsDiv);
    container.appendChild(descriptiveDiv);

    // Restore previous answer if exists
    const previousAnswer = this.answers[this.currentQuestionIndex];
    if (previousAnswer) {
      const radioInput = container.querySelector(`input[value="${previousAnswer}"]`);
      if (radioInput) {
        radioInput.checked = true;
        this.updateDescriptiveOptionStyles(optionsDiv);
      }
    }
  }

  /**
   * Get confidence level CSS class based on option index
   */
  getConfidenceLevelClass(index, totalOptions) {
    const ratio = index / (totalOptions - 1);
    if (ratio <= 0.33) return 'confidence-high';
    if (ratio <= 0.66) return 'confidence-medium';
    return 'confidence-low';
  }

  /**
   * Get confidence level text based on option index
   */
  getConfidenceLevelText(index, totalOptions) {
    const ratio = index / (totalOptions - 1);
    if (ratio <= 0.33) return 'High Confidence';
    if (ratio <= 0.66) return 'Medium Confidence';
    return 'Low Confidence';
  }

  /**
   * Update visual styles for descriptive options based on selection
   */
  updateDescriptiveOptionStyles(container) {
    const options = container.querySelectorAll('.descriptive-option');
    options.forEach(option => {
      const radio = option.querySelector('input[type="radio"]');
      const label = option.querySelector('.descriptive-option-label');

      if (radio.checked) {
        label.classList.add('selected');
      } else {
        label.classList.remove('selected');
      }
    });
  }

  /**
   * Setup event listeners for self-assessment questions
   */
  setupSelfAssessmentListeners(question) {
    // Handle practical task-focused options
    const practicalInputs = document.querySelectorAll(`input[name="practical-${this.currentQuestionIndex}"]`);
    practicalInputs.forEach(input => {
      input.addEventListener('change', () => {
        // Update visual selection
        this.updatePracticalOptionStyles();

        // Store the answer value and option text
        const optionText = input.getAttribute('data-option');
        this.handleAnswerSelection(input.value, optionText);
      });
    });

    // Restore previous answer if exists
    const previousAnswer = this.answers[this.currentQuestionIndex];
    if (previousAnswer) {
      const radioInput = document.querySelector(`input[name="practical-${this.currentQuestionIndex}"][value="${previousAnswer}"]`);
      if (radioInput) {
        radioInput.checked = true;
        this.updatePracticalOptionStyles();
      }
    }
  }

  /**
   * Handle star rating interaction
   */
  handleStarRating(rating, stars) {
    this.highlightStars(stars, rating);
  }

  /**
   * Highlight stars up to the given rating
   */
  highlightStars(stars, rating) {
    stars.forEach((star, index) => {
      if (index < rating) {
        star.classList.add('active');
      } else {
        star.classList.remove('active');
      }
    });
  }

  /**
   * Update visual styles for practical option selection
   */
  updatePracticalOptionStyles() {
    const practicalOptions = document.querySelectorAll(`input[name="practical-${this.currentQuestionIndex}"]`);
    practicalOptions.forEach(input => {
      const label = input.closest('.practical-option');
      if (input.checked) {
        label.classList.add('selected');
      } else {
        label.classList.remove('selected');
      }
    });
  }

  /**
   * Handle answer selection
   */
  handleAnswerSelection(answer, optionText = null) {
    this.answers[this.currentQuestionIndex] = answer;

    // Store additional metadata for practical assessments
    if (optionText) {
      if (!this.answerMetadata) {
        this.answerMetadata = {};
      }
      this.answerMetadata[this.currentQuestionIndex] = {
        value: answer,
        text: optionText,
        timestamp: Date.now()
      };
    }

    // Record interaction
    this.recordInteraction('answer_selected', {
      questionIndex: this.currentQuestionIndex,
      answer: answer,
      optionText: optionText,
      timestamp: Date.now()
    });

    // Update navigation buttons
    this.updateNavigationButtons();
  }

  /**
   * Record user interaction
   */
  recordInteraction(type, details) {
    if (!this.interactionLogs[this.currentQuestionIndex]) {
      this.interactionLogs[this.currentQuestionIndex] = [];
    }

    this.interactionLogs[this.currentQuestionIndex].push({
      type,
      details,
      timestamp: Date.now()
    });
  }

  /**
   * Update navigation buttons
   */
  updateNavigationButtons() {
    const prevButton = document.getElementById('prev-question');
    const nextButton = document.getElementById('next-question');
    const submitButton = document.getElementById('submit-assessment');

    // Previous button
    if (prevButton) {
      prevButton.disabled = this.currentQuestionIndex === 0;
    }

    // Next/Submit button logic
    const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;

    if (isLastQuestion) {
      if (nextButton) nextButton.classList.add('hidden');
      if (submitButton) submitButton.classList.remove('hidden');
    } else {
      if (nextButton) nextButton.classList.remove('hidden');
      if (submitButton) submitButton.classList.add('hidden');
    }
  }

  /**
   * Navigate between questions
   */
  navigateQuestion(direction) {
    const newIndex = this.currentQuestionIndex + direction;

    if (newIndex >= 0 && newIndex < this.questions.length) {
      this.currentQuestionIndex = newIndex;
      this.loadCurrentQuestion();
    }
  }



  /**
   * Confirm assessment submission
   */
  confirmSubmit() {
    // Allow submission without any confirmation prompts
    this.submitAssessment();
  }

  /**
   * Submit assessment for grading
   */
  async submitAssessment() {
    if (this.isSubmitted) {
      return; // Prevent double submission
    }

    this.isSubmitted = true;

    try {
      // Show enhanced loading screen for submission
      this.showLoading('Analyzing your responses...');

      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3003'
        : window.location.origin;

      const timeSpent = this.assessmentStartTime ? Date.now() - this.assessmentStartTime : 0;

      // Prepare assessment data
      const assessmentData = {
        answers: this.answers.map((answer, index) => ({
          questionId: this.questions[index].id,
          answer: answer || '', // Use empty string for unanswered questions
          timeSpent: this.questionStartTimes[index] ? Date.now() - this.questionStartTimes[index] : 0
        })),
        email: this.userData.email,
        level: this.currentLevel,
        timeSpent: timeSpent,
        userData: {
          firstName: this.userData.firstName,
          lastName: this.userData.lastName,
          name: this.userData.name,
          studentLevel: this.userData.studentLevel,
          userType: 'student'
        },
        detailedResponses: {
          assessmentMetadata: {
            startTime: this.assessmentStartTime,
            endTime: Date.now(),
            userAgent: navigator.userAgent,
            sessionId: this.assessmentId
          },
          interactionLogs: this.interactionLogs
        }
      };

      const response = await fetch(`${baseUrl}/api/digital-skills-assessments/${this.assessmentId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assessmentData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const results = await response.json();

      // Allow loading animation to complete before showing results
      setTimeout(() => {
        this.hideLoading();
        this.hideContainerWithTransition('assessment-questions', () => {
          this.showContainerWithTransition('assessment-results', 'flex');
          this.populateResults(results);
        });
      }, 1500);

    } catch (error) {
      console.error('Error submitting digital skills assessment:', error);
      this.hideLoading();
      this.isSubmitted = false; // Allow retry
      alert('Failed to submit assessment. Please try again.');
    }
  }

  /**
   * Populate assessment results data
   */
  populateResults(results) {
    // Update results display
    const resultsTitle = document.getElementById('results-title');
    const resultsScore = document.getElementById('results-score');
    const resultsContent = document.getElementById('results-content');

    if (resultsTitle) {
      resultsTitle.textContent = results.passed ?
        `Congratulations! You passed ${this.levelSpecs[this.currentLevel].name}!` :
        `Assessment Complete - ${this.levelSpecs[this.currentLevel].name}`;
    }

    if (resultsScore) {
      const scoreValue = resultsScore.querySelector('.score-value');
      const scoreTotal = resultsScore.querySelector('.score-total');

      if (scoreValue) scoreValue.textContent = results.score;
      if (scoreTotal) scoreTotal.textContent = `/ ${results.maxScore}`;

      // Add pass/fail styling
      resultsScore.className = `results-score ${results.passed ? 'passed' : 'needs-improvement'}`;
    }

    // Create detailed results content
    this.createResultsContent(results, resultsContent);

    // Show appropriate action buttons
    this.updateResultsActions(results);
  }

  /**
   * Create minimal results content for main display
   */
  createResultsContent(results, container) {
    if (!container) return;

    const passedClass = results.passed ? 'passed' : 'needs-improvement';
    const statusText = results.passed ? 'Achievement Unlocked' : 'Keep Learning';
    const statusIcon = results.passed ? '🎯' : '📚';

    // Store detailed results for modal
    this.detailedResults = results;

    container.innerHTML = `
      <div class="results-summary ${passedClass}">
        <div class="status-badge">
          <span class="status-icon">${statusIcon}</span>
          <span class="status-text">${statusText}</span>
        </div>
        <div class="skills-level-recommendation">
          ${this.createSkillsLevelDisplay(results)}
        </div>
      </div>

      <div class="results-details-minimal">
        <div class="performance-metrics">
          <div class="metric">
            <span class="metric-label">Score</span>
            <span class="metric-value">${results.score}/${results.maxScore}</span>
          </div>
          <div class="metric">
            <span class="metric-label">Percentage</span>
            <span class="metric-value">${Math.round((results.score / results.maxScore) * 100)}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">Time Taken</span>
            <span class="metric-value">${this.formatTimeDuration(this.assessmentStartTime ? Date.now() - this.assessmentStartTime : 0)}</span>
          </div>
        </div>

        <div class="minimal-recommendation">
          <h4>Recommended Next Step</h4>
          <p>${this.getMinimalRecommendation(results)}</p>
        </div>
      </div>
    `;
  }

  /**
   * Create skills level display
   */
  createSkillsLevelDisplay(results) {
    const skillsLevel = results.skillsLevel || results.level;
    const levelNames = {
      'EntryLevel2': 'Computer Skills - Beginners',
      'EntryLevel2Plus': 'Computer Skills - Beginners Plus',
      'EntryLevel3': 'Improvers Plus',
      'Level1': 'Computer Skills for Everyday Life',
      'Level2': 'Computer Skills for Work / ICDL Level 2',
      'Level3': 'Computer Skills for Work Level 2',
      'ICDLLevel2': 'ICDL Level 2',
      'ICDLLevel3': 'ICDL Level 3'
    };

    const levelName = levelNames[skillsLevel] || skillsLevel;
    const isRecommendedLevel = results.courseRecommendation && results.courseRecommendation.level !== results.level;

    return `
      <div class="skills-level-display">
        <h3 class="skills-level-title">
          ${isRecommendedLevel ? 'Recommended Level' : 'Current Level'}
        </h3>
        <div class="skills-level-badge ${skillsLevel.toLowerCase()}">
          <span class="level-icon">🎓</span>
          <span class="level-name">${levelName}</span>
        </div>
        ${isRecommendedLevel ? `
          <p class="level-reasoning">${results.courseRecommendation.reasoning}</p>
        ` : ''}
      </div>
    `;
  }

  /**
   * Create AI skills analysis section
   */
  createAISkillsAnalysis(results) {
    if (!results.feedback) return '';

    const feedback = results.feedback;
    const feedbackSections = [
      { key: 'basicComputerSkills', title: 'Basic Computer Skills', icon: '💻' },
      { key: 'internetAndEmail', title: 'Internet & Email', icon: '🌐' },
      { key: 'digitalSafety', title: 'Digital Safety', icon: '🔒' },
      { key: 'softwareApplications', title: 'Software Applications', icon: '📱' },
      { key: 'troubleshooting', title: 'Troubleshooting', icon: '🔧' }
    ];

    const sectionsHtml = feedbackSections
      .filter(section => feedback[section.key])
      .map(section => `
        <div class="feedback-item">
          <div class="feedback-header">
            <span class="feedback-icon">${section.icon}</span>
            <h4 class="feedback-title">${section.title}</h4>
          </div>
          <p class="feedback-text">${feedback[section.key]}</p>
        </div>
      `).join('');

    return `
      <div class="ai-skills-analysis">
        <h3 class="section-title">
          <span class="title-icon">🤖</span>
          AI Skills Analysis
        </h3>
        <div class="feedback-sections">
          ${sectionsHtml}
        </div>
        ${feedback.overall ? `
          <div class="overall-feedback">
            <h4 class="overall-title">Overall Assessment</h4>
            <p class="overall-text">${feedback.overall}</p>
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * Create confidence analysis section
   */
  createConfidenceAnalysis(confidenceAnalysis) {
    if (!confidenceAnalysis) return '';

    const confidenceColors = {
      'High': '#10b981',
      'Medium': '#f59e0b',
      'Low': '#ef4444'
    };

    const confidenceColor = confidenceColors[confidenceAnalysis.overallConfidence] || '#6b7280';

    return `
      <div class="confidence-analysis">
        <h3 class="section-title">
          <span class="title-icon">💪</span>
          Confidence Assessment
        </h3>
        <div class="confidence-overview">
          <div class="confidence-level">
            <span class="confidence-label">Overall Confidence:</span>
            <span class="confidence-badge" style="background-color: ${confidenceColor}">
              ${confidenceAnalysis.overallConfidence}
            </span>
          </div>
          <div class="practical-readiness">
            <span class="readiness-label">Practical Readiness:</span>
            <span class="readiness-value">${confidenceAnalysis.practicalReadiness}</span>
          </div>
        </div>

        ${confidenceAnalysis.confidenceAreas && confidenceAnalysis.confidenceAreas.length > 0 ? `
          <div class="confidence-areas">
            <h4 class="areas-title">Strong Confidence Areas</h4>
            <ul class="areas-list positive">
              ${confidenceAnalysis.confidenceAreas.map(area => `<li>${area}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        ${confidenceAnalysis.developmentAreas && confidenceAnalysis.developmentAreas.length > 0 ? `
          <div class="development-areas">
            <h4 class="areas-title">Areas for Development</h4>
            <ul class="areas-list development">
              ${confidenceAnalysis.developmentAreas.map(area => `<li>${area}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * Create course recommendations section
   */
  createCourseRecommendations(courseRecommendation) {
    if (!courseRecommendation) return '';

    const levelNames = {
      'EntryLevel2': 'Computer Skills - Beginners',
      'EntryLevel2Plus': 'Computer Skills - Beginners Plus',
      'EntryLevel3': 'Improvers Plus',
      'Level1': 'Computer Skills for Everyday Life',
      'Level2': 'Computer Skills for Work / ICDL Level 2',
      'Level3': 'ICDL Level 3'
    };

    const recommendedLevelName = levelNames[courseRecommendation.level] || courseRecommendation.level;

    return `
      <div class="course-recommendations">
        <h3 class="section-title">
          <span class="title-icon">🎯</span>
          Course Recommendations
        </h3>
        <div class="recommendation-card">
          <div class="recommended-course">
            <h4 class="course-title">${courseRecommendation.title || recommendedLevelName}</h4>
            <p class="course-reasoning">${courseRecommendation.reasoning}</p>
          </div>

          ${courseRecommendation.nextSteps && courseRecommendation.nextSteps.length > 0 ? `
            <div class="next-steps">
              <h4 class="steps-title">Next Steps</h4>
              <ul class="steps-list">
                ${courseRecommendation.nextSteps.map(step => `<li>${step}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }

  /**
   * Create strengths and improvements section
   */
  createStrengthsAndImprovements(strengths, improvements) {
    if (!strengths && !improvements) return '';

    return `
      <div class="strengths-improvements">
        ${strengths && strengths.length > 0 ? `
          <div class="strengths-section">
            <h3 class="section-title positive">
              <span class="title-icon">⭐</span>
              Your Strengths
            </h3>
            <ul class="strengths-list">
              ${strengths.map(strength => `<li class="strength-item">${strength}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        ${improvements && improvements.length > 0 ? `
          <div class="improvements-section">
            <h3 class="section-title development">
              <span class="title-icon">📈</span>
              Areas for Growth
            </h3>
            <ul class="improvements-list">
              ${improvements.map(improvement => `<li class="improvement-item">${improvement}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * Create topic breakdown pie chart display
   */
  createTopicBreakdown(topicBreakdown) {
    if (!topicBreakdown || Object.keys(topicBreakdown).length === 0) {
      return '';
    }

    const topicNames = {
      computerBasics: 'Computer Basics',
      mouseKeyboard: 'Mouse & Keyboard',
      basicOperations: 'Basic Operations',
      fileManagement: 'File Management',
      basicSafety: 'Basic Safety',
      laptopDesktop: 'Laptop & Desktop',
      applications: 'Applications',
      internetSafety: 'Internet Safety',
      emailBasics: 'Email Basics',
      digitalCitizenship: 'Digital Citizenship',
      microsoftApps: 'Microsoft Apps',
      onlineBanking: 'Online Banking',
      cloudStorage: 'Cloud Storage',
      digitalIdentity: 'Digital Identity',
      internetSkills: 'Internet Skills',
      advancedFormatting: 'Advanced Formatting',
      spreadsheets: 'Spreadsheets',
      presentations: 'Presentations',
      workplaceSkills: 'Workplace Skills',
      collaboration: 'Collaboration',
      operatingSystems: 'Operating Systems',
      emailProficiency: 'Email Proficiency',
      onlineTransactions: 'Online Transactions',
      digitalSafety: 'Digital Safety',
      troubleshooting: 'Troubleshooting',
      timedExam: 'Timed Exam',
      wordAdvanced: 'Advanced Word',
      excelAdvanced: 'Advanced Excel',
      powerpointAdvanced: 'Advanced PowerPoint',
      employmentSkills: 'Employment Skills',
      advancedMicrosoft: 'Advanced Microsoft',
      itCareers: 'IT Careers',
      higherEducation: 'Higher Education',
      professionalSkills: 'Professional Skills',
      certification: 'Certification'
    };

    // Generate unique ID for this chart
    const chartId = 'topic-pie-chart-' + Date.now();

    // Prepare data for pie chart
    const chartData = Object.entries(topicBreakdown).map(([topic, data]) => ({
      name: topicNames[topic] || topic,
      percentage: data.percentage || 0,
      correct: data.correct,
      total: data.total
    }));

    // Create legend items
    const legendItems = chartData.map((item, index) => {
      const color = this.getPieChartColor(index);
      return `
        <div class="legend-item">
          <div class="legend-color" style="background-color: ${color}"></div>
          <div class="legend-text">
            <div class="legend-label">${item.name}</div>
            <div class="legend-value">${item.correct}/${item.total} (${item.percentage}%)</div>
          </div>
        </div>
      `;
    }).join('');

    // Schedule pie chart creation after DOM insertion
    setTimeout(() => {
      this.createPieChart(chartId, chartData);
    }, 100);

    return `
      <div class="topic-performance-chart">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          Performance by Topic
        </h3>
        <div class="chart-container">
          <div class="pie-chart-wrapper">
            <canvas id="${chartId}" class="pie-chart"></canvas>
          </div>
          <div class="chart-legend">
            ${legendItems}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Get color for pie chart segment
   */
  getPieChartColor(index) {
    const colors = [
      '#3b82f6', // Blue
      '#10b981', // Green
      '#f59e0b', // Orange
      '#ef4444', // Red
      '#8b5cf6', // Purple
      '#06b6d4', // Cyan
      '#84cc16', // Lime
      '#f97316', // Orange-red
      '#ec4899', // Pink
      '#6366f1'  // Indigo
    ];
    return colors[index % colors.length];
  }

  /**
   * Create pie chart using Canvas API
   */
  createPieChart(canvasId, data) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      console.error('Canvas element not found:', canvasId);
      return;
    }

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    // Set canvas size
    canvas.width = 300;
    canvas.height = 300;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate total and angles
    const total = data.reduce((sum, item) => sum + item.percentage, 0);
    let currentAngle = -Math.PI / 2; // Start from top

    // Draw pie segments
    data.forEach((item, index) => {
      const sliceAngle = (item.percentage / total) * 2 * Math.PI;
      const color = this.getPieChartColor(index);

      // Draw slice
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      ctx.fillStyle = color;
      ctx.fill();

      // Draw border
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();

      currentAngle += sliceAngle;
    });

    // Draw center circle for donut effect (optional)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.4, 0, 2 * Math.PI);
    ctx.fillStyle = '#ffffff';
    ctx.fill();
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 2;
    ctx.stroke();
  }

  /**
   * Create recommendations display
   */
  createRecommendations(results) {
    const strengths = results.strengths || [];
    const improvements = results.improvements || [];
    const recommendations = results.recommendations || [];
    const nextSteps = results.nextSteps || [];

    let content = '';

    if (strengths.length > 0) {
      content += `
        <div class="recommendations-section strengths">
          <h4 class="section-title">💪 Strengths</h4>
          <ul class="recommendation-list">
            ${strengths.map(strength => `<li>${strength}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    if (improvements.length > 0) {
      content += `
        <div class="recommendations-section improvements">
          <h4 class="section-title">📈 Areas for Improvement</h4>
          <ul class="recommendation-list">
            ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    if (recommendations.length > 0) {
      content += `
        <div class="recommendations-section recommendations">
          <h4 class="section-title">💡 Recommendations</h4>
          <ul class="recommendation-list">
            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    if (nextSteps.length > 0) {
      content += `
        <div class="recommendations-section next-steps">
          <h4 class="section-title">🎯 Next Steps</h4>
          <ul class="recommendation-list">
            ${nextSteps.map(step => `<li>${step}</li>`).join('')}
          </ul>
        </div>
      `;
    }

    return content;
  }

  /**
   * Update results action buttons
   */
  updateResultsActions(results) {
    const nextLevelButton = document.getElementById('next-level');

    // Show next level button if passed and not at highest level
    if (results.passed && nextLevelButton) {
      const currentLevelIndex = Object.keys(this.levelSpecs).indexOf(this.currentLevel);
      const hasNextLevel = currentLevelIndex < Object.keys(this.levelSpecs).length - 1;

      if (hasNextLevel) {
        nextLevelButton.classList.remove('hidden');
        const nextLevel = Object.keys(this.levelSpecs)[currentLevelIndex + 1];
        const nextLevelName = this.levelSpecs[nextLevel].name;
        nextLevelButton.querySelector('.btn-text').textContent = `Try ${nextLevelName}`;
      }
    }
  }

  /**
   * Get minimal recommendation for main results display
   */
  getMinimalRecommendation(results) {
    if (results.passed) {
      return "Congratulations! You're ready to progress to the next level or apply these skills in practice.";
    } else {
      return "Focus on strengthening your foundational skills. Consider enrolling in a basic computer skills course.";
    }
  }

  /**
   * Show detailed report modal with comprehensive analysis
   */
  showDetailedReport() {
    if (!this.detailedResults) {
      console.error('No detailed results available');
      return;
    }

    this.createDetailedReportModal();
  }

  /**
   * Create and show the detailed report modal
   */
  createDetailedReportModal() {
    const results = this.detailedResults;

    // Remove existing modal if present
    const existingModal = document.getElementById('digital-skills-detailed-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'digital-skills-detailed-modal';
    modalOverlay.className = 'modal-overlay';

    modalOverlay.innerHTML = `
      <div class="modal-content detailed-report-modal">
        <div class="modal-header">
          <h2>Digital Skills Assessment - Detailed Report</h2>
          <button class="modal-close" onclick="this.closest('.modal-overlay').remove(); document.body.style.overflow = 'auto';">×</button>
        </div>
        <div class="modal-body">
          <div class="detailed-analysis">
            ${this.createAISkillsAnalysis(results)}
            ${this.createTopicBreakdown(results.topicBreakdown)}
            ${this.createConfidenceAnalysis(results.confidenceAnalysis)}
            ${this.createCourseRecommendations(results.courseRecommendation)}
            ${this.createStrengthsAndImprovements(results.strengths, results.improvements)}
          </div>
        </div>
      </div>
    `;

    // Add to document
    document.body.appendChild(modalOverlay);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Show modal with animation
    setTimeout(() => {
      modalOverlay.classList.add('show');
    }, 10);

    // Close on overlay click
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        modalOverlay.remove();
        document.body.style.overflow = 'auto';
      }
    });
  }

  /**
   * Retake assessment
   */
  retakeAssessment() {
    if (confirm('Are you sure you want to retake this assessment? Your current results will be lost.')) {
      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.isSubmitted = false;

      // Clear performance data
      this.performanceData = {};
      this.interactionLogs = {};
      this.questionStartTimes = [];

      // Start assessment again
      this.startAssessment();
    }
  }

  /**
   * Progress to next level
   */
  async progressToNextLevel() {
    const currentLevelIndex = Object.keys(this.levelSpecs).indexOf(this.currentLevel);
    const nextLevel = Object.keys(this.levelSpecs)[currentLevelIndex + 1];

    if (!nextLevel) {
      alert('You have completed the highest level available!');
      return;
    }

    try {
      // Update current level
      this.currentLevel = nextLevel;

      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.isSubmitted = false;

      // Clear performance data
      this.performanceData = {};
      this.interactionLogs = {};
      this.questionStartTimes = [];

      // Start assessment for new level
      this.startAssessment();

    } catch (error) {
      console.error('Error progressing to next level:', error);
      alert('Failed to progress to next level. Please try again.');
    }
  }
}
