<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>All Interactive Questions Test - Mathematics Assessment</title>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link rel="stylesheet" type="text/css" href="mathInteractive.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Interact.js for drag and drop functionality -->
    <script src="https://cdn.jsdelivr.net/npm/interactjs/dist/interact.min.js"></script>
</head>

<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-center mb-8 text-blue-900">Interactive Questions Comprehensive Test</h1>
        
        <!-- Test Controls -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test All Interactive Question Types</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button id="test-number-line" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Number Line
                </button>
                <button id="test-drag-drop" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Drag & Drop
                </button>
                <button id="test-visual-calculator" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Visual Calculator
                </button>
                <button id="test-step-by-step" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Step-by-Step
                </button>
                <button id="test-coordinate-plot" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Coordinate Plot
                </button>
                <button id="test-ratio-slider" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Ratio Slider
                </button>
                <button id="test-equation-builder" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Equation Builder
                </button>
                <button id="test-pattern-completion" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                    Pattern Completion
                </button>
            </div>
            <div class="mt-4 flex gap-4">
                <button id="test-all" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">
                    Test All Types
                </button>
                <button id="clear-all" class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
                    Clear All
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="bg-white rounded-lg shadow-lg p-6 mb-8 hidden">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="results-content" class="space-y-2"></div>
        </div>

        <!-- Interactive Question Container -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Interactive Question Display</h2>
            <div id="current-question-text" class="text-lg mb-4 font-medium text-gray-800"></div>
            
            <!-- Number Line Slider -->
            <div id="number-line-slider" class="interactive-question hidden">
                <div class="number-line-container">
                    <div class="number-line-labels" id="number-line-labels"></div>
                    <div class="number-line-track" id="number-line-track">
                        <div class="number-line-handle" id="number-line-handle" tabindex="0" role="slider"></div>
                    </div>
                    <div class="number-line-value" id="number-line-value">0</div>
                </div>
                <p class="input-hint">Drag the handle to select your answer</p>
            </div>

            <!-- Drag and Drop Matching -->
            <div id="drag-drop-matching" class="interactive-question hidden">
                <div class="matching-container">
                    <div class="draggable-items" id="draggable-items"></div>
                    <div class="drop-zones" id="drop-zones"></div>
                </div>
                <button id="reset-drag-drop-btn" class="reset-btn mt-4">Reset</button>
                <p class="input-hint">Drag items to their correct positions</p>
            </div>

            <!-- Visual Calculator -->
            <div id="visual-calculator" class="interactive-question hidden">
                <div class="calculator-container">
                    <div class="calculator-display">
                        <div class="calculation-steps" id="calculation-steps">
                            <!-- Calculation steps will be shown here -->
                        </div>
                        <div class="current-display" id="current-display">0</div>
                    </div>
                    <div class="calculator-keypad" id="calculator-keypad">
                        <!-- Calculator buttons will be dynamically generated -->
                    </div>
                    <div class="calculator-controls">
                        <button id="clear-calculator-btn" class="reset-btn">Clear</button>
                        <button id="step-calculator-btn" class="step-btn">Next Step</button>
                    </div>
                </div>
                <p class="input-hint">Use the calculator to solve the problem step by step</p>
            </div>

            <!-- Step-by-step Guided Problems -->
            <div id="step-by-step-guided" class="interactive-question hidden">
                <div class="guided-problem-container">
                    <div class="current-step">
                        <h4 id="step-instruction" class="step-instruction">Step 1: Follow the instruction</h4>
                        <div class="step-input-container">
                            <input type="text" id="step-answer" class="step-input" placeholder="Enter your answer">
                            <button id="check-step-btn" class="check-btn">Check</button>
                        </div>
                        <div id="step-feedback" class="step-feedback hidden"></div>
                        <div id="step-hint" class="step-hint hidden"></div>
                    </div>
                    <div class="progress-indicator">
                        <span id="current-step-number">1</span> of <span id="total-steps">3</span>
                    </div>
                </div>
                <p class="input-hint">Complete each step to solve the problem</p>
            </div>

            <!-- Coordinate Plotting -->
            <div id="coordinate-plotting" class="interactive-question hidden">
                <div class="coordinate-container">
                    <canvas id="coordinate-canvas" class="interactive-canvas"></canvas>
                    <div class="coordinate-controls">
                        <button id="clear-points-btn" class="reset-btn">Clear Points</button>
                        <div class="coordinate-display">
                            <span id="current-coordinates">(0, 0)</span>
                        </div>
                    </div>
                </div>
                <p class="input-hint">Click on the grid to plot points</p>
            </div>

            <!-- Ratio Sliders -->
            <div id="ratio-sliders" class="interactive-question hidden">
                <div class="ratio-container">
                    <div id="ratio-sliders-container" class="sliders-container"></div>
                    <div class="ratio-display">
                        <span id="current-ratio">1:1</span>
                    </div>
                    <button id="reset-ratio-btn" class="reset-btn mt-4">Reset</button>
                </div>
                <p class="input-hint">Adjust the sliders to show the correct ratio</p>
            </div>

            <!-- Equation Builders -->
            <div id="equation-builders" class="interactive-question hidden">
                <div class="equation-builder-container">
                    <div id="available-terms" class="terms-container">
                        <h5>Available Terms:</h5>
                        <div id="terms-list" class="terms-list"></div>
                    </div>
                    <div id="equation-workspace" class="equation-workspace">
                        <h5>Build your equation:</h5>
                        <div id="equation-area" class="equation-area"></div>
                    </div>
                    <button id="clear-equation-btn" class="reset-btn mt-4">Clear</button>
                </div>
                <p class="input-hint">Drag terms to build the equation</p>
            </div>

            <!-- Pattern Completion -->
            <div id="pattern-completion" class="interactive-question hidden">
                <div class="pattern-container">
                    <div class="pattern-display">
                        <canvas id="pattern-canvas" class="interactive-canvas"></canvas>
                    </div>
                    <div class="pattern-input-area">
                        <div class="pattern-options" id="pattern-options">
                            <!-- Pattern options will be dynamically generated -->
                        </div>
                        <div class="pattern-answer-area" id="pattern-answer-area">
                            <span class="pattern-prompt">What comes next?</span>
                            <div class="pattern-answer-slot" id="pattern-answer-slot">
                                <span class="slot-placeholder">?</span>
                            </div>
                        </div>
                    </div>
                    <div class="pattern-controls">
                        <button id="clear-pattern-btn" class="reset-btn">Clear</button>
                        <button id="hint-pattern-btn" class="hint-btn">Hint</button>
                    </div>
                </div>
                <p class="input-hint">Look at the pattern and select what comes next</p>
            </div>

            <!-- Test Status -->
            <div class="mt-6 p-4 bg-gray-50 rounded">
                <div id="test-status" class="text-sm text-gray-600">Ready to test interactive questions</div>
                <button id="next-question-btn" class="bg-blue-600 text-white px-6 py-2 rounded mt-4 hover:bg-blue-700" disabled>
                    Next Question (Test)
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="interactiveQuestionData.js"></script>
    <script src="mathAssessment.js"></script>
    <script>
        // Comprehensive test implementation
        class InteractiveQuestionTester {
            constructor() {
                this.currentQuestionType = null;
                this.testResults = [];
                this.setupEventListeners();
                this.initializeMockAssessment();
            }

            initializeMockAssessment() {
                // Create a mock assessment object with the necessary methods
                window.mathAssessment = {
                    currentNumberLineValue: 0,
                    numberLineConfig: {},
                    dragDropState: null,
                    areaModelState: null,
                    stepByStepState: null,
                    coordinatePlottingState: null,
                    ratioSlidersState: null,
                    equationBuilderState: null,
                    balanceScalesState: null,
                    
                    // Import methods from the main assessment
                    showNumberLineSlider: this.showNumberLineSlider.bind(this),
                    showDragDropMatching: this.showDragDropMatching.bind(this),
                    showAreaModels: this.showAreaModels.bind(this),
                    showStepByStepGuided: this.showStepByStepGuided.bind(this),
                    showCoordinatePlotting: this.showCoordinatePlotting.bind(this),
                    showRatioSliders: this.showRatioSliders.bind(this),
                    showEquationBuilders: this.showEquationBuilders.bind(this),
                    showBalanceScales: this.showBalanceScales.bind(this),
                    
                    isInteractiveQuestion: (question) => {
                        const interactiveTypes = [
                            'number-line', 'drag-drop', 'area-model', 'step-by-step',
                            'coordinate-plot', 'ratio-slider', 'equation-builder', 'balance-scale'
                        ];
                        return interactiveTypes.includes(question.type);
                    }
                };
            }

            setupEventListeners() {
                // Individual test buttons
                document.getElementById('test-number-line').addEventListener('click', () => this.testQuestionType('number-line'));
                document.getElementById('test-drag-drop').addEventListener('click', () => this.testQuestionType('drag-drop'));
                document.getElementById('test-visual-calculator').addEventListener('click', () => this.testQuestionType('visual-calculator'));
                document.getElementById('test-step-by-step').addEventListener('click', () => this.testQuestionType('step-by-step'));
                document.getElementById('test-coordinate-plot').addEventListener('click', () => this.testQuestionType('coordinate-plot'));
                document.getElementById('test-ratio-slider').addEventListener('click', () => this.testQuestionType('ratio-slider'));
                document.getElementById('test-equation-builder').addEventListener('click', () => this.testQuestionType('equation-builder'));
                document.getElementById('test-pattern-completion').addEventListener('click', () => this.testQuestionType('pattern-completion'));
                
                // Control buttons
                document.getElementById('test-all').addEventListener('click', () => this.testAllTypes());
                document.getElementById('clear-all').addEventListener('click', () => this.clearAll());
                
                // Mock next button
                document.getElementById('next-question-btn').addEventListener('click', () => {
                    this.showResult('Next Button', 'Next button clicked - test successful!');
                });
            }

            testQuestionType(type) {
                this.clearAll();
                this.currentQuestionType = type;
                
                // Get sample question from data
                const question = window.InteractiveQuestionData ? 
                    window.InteractiveQuestionData.getQuestions('Entry', type)[0] ||
                    window.InteractiveQuestionData.getQuestions('Level1', type)[0] ||
                    window.InteractiveQuestionData.getQuestions('GCSEPart1', type)[0] ||
                    window.InteractiveQuestionData.getQuestions('GCSEPart2', type)[0]
                    : null;
                
                if (!question) {
                    this.showResult(type, 'No sample question found');
                    return;
                }
                
                // Display question text
                document.getElementById('current-question-text').textContent = question.question;
                
                // Test the specific question type
                try {
                    this.testSpecificType(type, question);
                    this.showResult(type, `Successfully loaded: ${question.question}`);
                    this.updateTestStatus(`Testing ${type} - interact with the question above`);
                } catch (error) {
                    this.showResult(type, `Error: ${error.message}`);
                    console.error('Test error:', error);
                }
            }

            testSpecificType(type, question) {
                // Call the actual implementation methods
                try {
                    switch(type) {
                        case 'number-line':
                            window.mathAssessment.showNumberLineSlider(question);
                            break;
                        case 'drag-drop':
                            window.mathAssessment.showDragDropMatching(question);
                            break;
                        case 'visual-calculator':
                            window.mathAssessment.showVisualCalculator(question);
                            break;
                        case 'step-by-step':
                            window.mathAssessment.showStepByStepGuided(question);
                            break;
                        case 'coordinate-plot':
                            window.mathAssessment.showCoordinatePlotting(question);
                            break;
                        case 'ratio-slider':
                            window.mathAssessment.showRatioSliders(question);
                            break;
                        case 'equation-builder':
                            window.mathAssessment.showEquationBuilders(question);
                            break;
                        case 'pattern-completion':
                            window.mathAssessment.showPatternCompletion(question);
                            break;
                        default:
                            throw new Error(`Unknown question type: ${type}`);
                    }
                    console.log(`Successfully loaded ${type}:`, question);
                } catch (error) {
                    console.error(`Error testing ${type}:`, error);
                    throw error;
                }
            }

            getContainerIdForType(type) {
                const typeMap = {
                    'number-line': 'number-line-slider',
                    'drag-drop': 'drag-drop-matching',
                    'visual-calculator': 'visual-calculator',
                    'step-by-step': 'step-by-step-guided',
                    'coordinate-plot': 'coordinate-plotting',
                    'ratio-slider': 'ratio-sliders',
                    'equation-builder': 'equation-builders',
                    'pattern-completion': 'pattern-completion'
                };
                return typeMap[type];
            }

            testAllTypes() {
                const types = ['number-line', 'drag-drop', 'visual-calculator', 'number-bonds', 'step-by-step',
                              'coordinate-plot', 'ratio-slider', 'equation-builder', 'pattern-completion'];
                
                let index = 0;
                const testNext = () => {
                    if (index < types.length) {
                        this.testQuestionType(types[index]);
                        index++;
                        setTimeout(testNext, 2000); // 2 second delay between tests
                    } else {
                        this.showResult('All Tests', 'All interactive question types tested!');
                    }
                };
                
                testNext();
            }

            clearAll() {
                // Hide all interactive containers
                const containers = [
                    'number-line-slider', 'drag-drop-matching', 'visual-calculator', 'step-by-step-guided',
                    'coordinate-plotting', 'ratio-sliders', 'equation-builders', 'pattern-completion'
                ];
                
                containers.forEach(id => {
                    const container = document.getElementById(id);
                    if (container) {
                        container.classList.add('hidden');
                    }
                });
                
                document.getElementById('current-question-text').textContent = '';
                document.getElementById('test-results').classList.add('hidden');
                this.updateTestStatus('Ready to test interactive questions');
                this.currentQuestionType = null;
            }

            showResult(testName, message) {
                const resultsContainer = document.getElementById('test-results');
                const resultsContent = document.getElementById('results-content');
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'p-3 bg-blue-100 border border-blue-300 rounded';
                resultDiv.innerHTML = `<strong>${testName}:</strong> ${message}`;
                
                resultsContent.appendChild(resultDiv);
                resultsContainer.classList.remove('hidden');
                
                // Store result
                this.testResults.push({ testName, message, timestamp: new Date() });
            }

            updateTestStatus(status) {
                document.getElementById('test-status').textContent = status;
            }

            // Placeholder methods for actual implementations
            showNumberLineSlider(question) { console.log('Number line slider test:', question); }
            showDragDropMatching(question) { console.log('Drag drop matching test:', question); }
            showAreaModels(question) { console.log('Area models test:', question); }
            showStepByStepGuided(question) { console.log('Step by step test:', question); }
            showCoordinatePlotting(question) { console.log('Coordinate plotting test:', question); }
            showRatioSliders(question) { console.log('Ratio sliders test:', question); }
            showEquationBuilders(question) { console.log('Equation builders test:', question); }
            showBalanceScales(question) { console.log('Balance scales test:', question); }
        }

        // Initialize tester when page loads
        document.addEventListener('DOMContentLoaded', function() {
            window.tester = new InteractiveQuestionTester();
            
            // Test data availability
            if (window.InteractiveQuestionData) {
                const stats = window.InteractiveQuestionData.getStatistics();
                console.log('Interactive question data loaded:', stats);
                window.tester.showResult('Data Load', `Loaded ${stats.totalQuestions} interactive questions`);
            } else {
                console.error('Interactive question data not loaded');
                window.tester.showResult('Data Load', 'ERROR: Interactive question data not loaded');
            }
        });
    </script>
</body>
</html>
