<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Data Collection Test</title>
    <link rel="stylesheet" href="mathAssessment.css">
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-results {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-error {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            color: #721c24;
        }
        
        .performance-data {
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .metric-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
        }
        
        .metric-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 18px;
            color: #007bff;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Interactive Question Performance Data Collection Test</h1>
        <p>This test validates that comprehensive performance data is being collected and properly formatted for AI analysis.</p>
        
        <div class="test-section">
            <h2>Test Controls</h2>
            <button class="test-button" onclick="testAllQuestionTypes()">Test All Question Types</button>
            <button class="test-button" onclick="testPerformanceTracking()">Test Performance Tracking</button>
            <button class="test-button" onclick="testDataTransmission()">Test Data Transmission</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div class="test-section">
            <h2>Performance Data Collection Status</h2>
            <div id="collection-status">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-title">Question Types Tested</div>
                        <div class="metric-value" id="types-tested">0/8</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Performance Metrics Collected</div>
                        <div class="metric-value" id="metrics-collected">0</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">Interaction Logs Captured</div>
                        <div class="metric-value" id="interactions-logged">0</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">AI Analysis Ready</div>
                        <div class="metric-value" id="ai-ready">No</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Question Type Tests</h2>
            <div id="question-type-tests">
                <div class="test-item" data-type="number-line">
                    <span class="status-indicator status-pending"></span>
                    Number Line Slider - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="drag-drop">
                    <span class="status-indicator status-pending"></span>
                    Drag & Drop Matching - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="visual-calculator">
                    <span class="status-indicator status-pending"></span>
                    Visual Calculator - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="number-bonds">
                    <span class="status-indicator status-pending"></span>
                    Number Bonds - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="step-by-step">
                    <span class="status-indicator status-pending"></span>
                    Step-by-Step Guided - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="coordinate-plot">
                    <span class="status-indicator status-pending"></span>
                    Coordinate Plotting - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="ratio-slider">
                    <span class="status-indicator status-pending"></span>
                    Ratio Sliders - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="equation-builder">
                    <span class="status-indicator status-pending"></span>
                    Equation Builder - <span class="status">Pending</span>
                </div>
                <div class="test-item" data-type="pattern-completion">
                    <span class="status-indicator status-pending"></span>
                    Pattern Completion - <span class="status">Pending</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Performance Data Sample</h2>
            <div id="performance-data-display">
                <p>Run tests to see collected performance data...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>AI Analysis Integration Test</h2>
            <div id="ai-integration-results">
                <p>Test data transmission to see AI analysis integration status...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test Results Log</h2>
            <div id="test-log">
                <p>Test results will appear here...</p>
            </div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="interactiveQuestionData.js"></script>
    <script src="mathAssessment.js"></script>
    
    <script>
        let testResults = {
            questionTypes: {},
            performanceData: {},
            transmissionTest: null
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type === 'error' ? 'test-error' : 'test-results';
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[Performance Test] ${message}`);
        }

        function updateStatus() {
            const typesTestedCount = Object.keys(testResults.questionTypes).length;
            document.getElementById('types-tested').textContent = `${typesTestedCount}/8`;
            
            const totalMetrics = Object.values(testResults.performanceData).reduce((sum, data) => {
                return sum + (data.performanceMetrics ? Object.keys(data.performanceMetrics).length : 0);
            }, 0);
            document.getElementById('metrics-collected').textContent = totalMetrics;
            
            const totalInteractions = Object.values(testResults.performanceData).reduce((sum, data) => {
                return sum + (data.interactionSequence ? data.interactionSequence.length : 0);
            }, 0);
            document.getElementById('interactions-logged').textContent = totalInteractions;
            
            const aiReady = testResults.transmissionTest && testResults.transmissionTest.success;
            document.getElementById('ai-ready').textContent = aiReady ? 'Yes' : 'No';
        }

        function updateQuestionTypeStatus(type, status) {
            const testItem = document.querySelector(`[data-type="${type}"]`);
            if (testItem) {
                const indicator = testItem.querySelector('.status-indicator');
                const statusText = testItem.querySelector('.status');
                
                indicator.className = `status-indicator status-${status}`;
                statusText.textContent = status === 'pass' ? 'Pass' : status === 'fail' ? 'Fail' : 'Pending';
            }
        }

        async function testAllQuestionTypes() {
            log('Starting comprehensive question type testing...');
            
            const questionTypes = [
                'number-line', 'drag-drop', 'visual-calculator', 'number-bonds',
                'step-by-step', 'coordinate-plot', 'ratio-slider', 'equation-builder', 'pattern-completion'
            ];
            
            for (const type of questionTypes) {
                await testQuestionType(type);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
            }
            
            log('All question type tests completed!');
            updateStatus();
        }

        async function testQuestionType(type) {
            try {
                log(`Testing ${type} performance data collection...`);
                
                // Initialize math assessment if not already done
                if (!window.mathAssessment) {
                    window.mathAssessment = new MathAssessment();
                }
                
                // Get a sample question of this type
                const question = window.InteractiveQuestionData.getQuestionsByType(type)[0];
                if (!question) {
                    throw new Error(`No sample question found for type: ${type}`);
                }
                
                // Initialize performance tracking
                window.mathAssessment.initializeQuestionPerformanceTracking(question);
                
                // Simulate some interactions
                window.mathAssessment.logInteraction('first_interaction', { type: 'test' });
                window.mathAssessment.logInteraction('attempt', { attemptNumber: 1 });
                
                if (Math.random() > 0.5) {
                    window.mathAssessment.logInteraction('hint_request', { hintType: 'guidance' });
                }
                
                if (Math.random() > 0.7) {
                    window.mathAssessment.logInteraction('error', { errorType: 'input_validation' });
                }
                
                window.mathAssessment.logInteraction('solution_step', { step: 'test_step', progress: 0.5 });
                
                // Finalize performance tracking
                window.mathAssessment.finalizeQuestionPerformance(true);
                
                // Get the answer with performance data
                let answerData;
                switch (type) {
                    case 'drag-drop':
                        answerData = window.mathAssessment.getDragDropAnswer();
                        break;
                    case 'visual-calculator':
                        answerData = window.mathAssessment.getVisualCalculatorAnswer();
                        break;
                    case 'number-bonds':
                        answerData = window.mathAssessment.getNumberBondsAnswer();
                        break;
                    case 'step-by-step':
                        answerData = window.mathAssessment.getStepByStepAnswer();
                        break;
                    case 'coordinate-plot':
                        answerData = window.mathAssessment.getCoordinatePlottingAnswer();
                        break;
                    case 'ratio-slider':
                        answerData = window.mathAssessment.getRatioSlidersAnswer();
                        break;
                    case 'equation-builder':
                        answerData = window.mathAssessment.getEquationBuilderAnswer();
                        break;
                    case 'pattern-completion':
                        answerData = window.mathAssessment.getPatternCompletionAnswer();
                        break;
                    default:
                        answerData = JSON.stringify({ type: type, testData: true });
                }
                
                const parsedData = JSON.parse(answerData);
                
                // Validate performance data structure
                const hasPerformanceMetrics = parsedData.performanceMetrics !== undefined;
                const hasInteractionSequence = parsedData.interactionSequence !== undefined;
                const hasTimestamp = parsedData.timestamp !== undefined;
                
                if (hasPerformanceMetrics && hasInteractionSequence && hasTimestamp) {
                    testResults.questionTypes[type] = 'pass';
                    testResults.performanceData[type] = parsedData;
                    updateQuestionTypeStatus(type, 'pass');
                    log(`✅ ${type}: Performance data collection successful`);
                } else {
                    testResults.questionTypes[type] = 'fail';
                    updateQuestionTypeStatus(type, 'fail');
                    log(`❌ ${type}: Missing performance data components`, 'error');
                }
                
            } catch (error) {
                testResults.questionTypes[type] = 'fail';
                updateQuestionTypeStatus(type, 'fail');
                log(`❌ ${type}: Test failed - ${error.message}`, 'error');
            }
        }

        async function testPerformanceTracking() {
            log('Testing performance tracking system...');
            
            try {
                // Test performance tracking initialization
                if (!window.mathAssessment) {
                    window.mathAssessment = new MathAssessment();
                }
                
                const testQuestion = {
                    id: 'test-question',
                    type: 'drag-drop',
                    topic: 'arithmetic'
                };
                
                window.mathAssessment.initializeQuestionPerformanceTracking(testQuestion);
                
                // Test interaction logging
                window.mathAssessment.logInteraction('test_interaction', { data: 'test' });
                
                // Test performance finalization
                window.mathAssessment.finalizeQuestionPerformance(true);
                
                // Validate performance data structure
                const performanceData = window.mathAssessment.currentQuestionPerformance;
                
                if (performanceData && performanceData.interactions && performanceData.difficultyIndicators) {
                    log('✅ Performance tracking system working correctly');
                    
                    // Display sample performance data
                    const displayDiv = document.getElementById('performance-data-display');
                    displayDiv.innerHTML = `<div class="performance-data">${JSON.stringify(performanceData, null, 2)}</div>`;
                    
                } else {
                    throw new Error('Performance tracking data structure incomplete');
                }
                
            } catch (error) {
                log(`❌ Performance tracking test failed: ${error.message}`, 'error');
            }
        }

        async function testDataTransmission() {
            log('Testing data transmission to AI analysis system...');
            
            try {
                // Simulate assessment data with performance metrics
                const mockAssessmentData = {
                    answers: Object.values(testResults.performanceData).map((data, index) => ({
                        questionId: `test-${index}`,
                        questionType: data.type,
                        topic: 'test-topic',
                        studentAnswer: JSON.stringify(data),
                        timeSpent: 30000,
                        performanceMetrics: data.performanceMetrics,
                        interactionSequence: data.interactionSequence || [],
                        questionAnalysis: {
                            questionType: data.type,
                            cognitiveLoad: 3,
                            answerQuality: { completeness: true, accuracy: true }
                        }
                    })),
                    email: '<EMAIL>',
                    level: 'Entry',
                    timeSpent: 300000,
                    performanceAnalytics: {
                        totalAssessmentTime: 300000,
                        questionsAttempted: Object.keys(testResults.performanceData).length,
                        aggregateMetrics: {
                            averageTimePerQuestion: 30000,
                            averageEfficiency: 0.75,
                            completionRate: 1.0
                        }
                    }
                };
                
                // Test data structure validation
                const requiredFields = ['answers', 'performanceAnalytics', 'email', 'level'];
                const missingFields = requiredFields.filter(field => !mockAssessmentData[field]);
                
                if (missingFields.length > 0) {
                    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
                }
                
                // Validate performance data in answers
                const answersWithPerformance = mockAssessmentData.answers.filter(a => a.performanceMetrics);
                const performanceDataRatio = answersWithPerformance.length / mockAssessmentData.answers.length;
                
                if (performanceDataRatio < 0.5) {
                    throw new Error(`Insufficient performance data: only ${Math.round(performanceDataRatio * 100)}% of answers have performance metrics`);
                }
                
                testResults.transmissionTest = {
                    success: true,
                    dataStructure: 'valid',
                    performanceDataRatio: performanceDataRatio,
                    totalDataSize: JSON.stringify(mockAssessmentData).length
                };
                
                log(`✅ Data transmission test passed`);
                log(`📊 Performance data coverage: ${Math.round(performanceDataRatio * 100)}%`);
                log(`📦 Total data size: ${Math.round(testResults.transmissionTest.totalDataSize / 1024)} KB`);
                
                // Display AI integration status
                const aiDiv = document.getElementById('ai-integration-results');
                aiDiv.innerHTML = `
                    <div class="test-results">
                        <h4>✅ AI Analysis Integration Ready</h4>
                        <p><strong>Data Structure:</strong> Valid</p>
                        <p><strong>Performance Coverage:</strong> ${Math.round(performanceDataRatio * 100)}%</p>
                        <p><strong>Data Size:</strong> ${Math.round(testResults.transmissionTest.totalDataSize / 1024)} KB</p>
                        <p><strong>Interactive Questions:</strong> ${answersWithPerformance.length}</p>
                        <div class="performance-data">${JSON.stringify(mockAssessmentData.performanceAnalytics, null, 2)}</div>
                    </div>
                `;
                
            } catch (error) {
                testResults.transmissionTest = { success: false, error: error.message };
                log(`❌ Data transmission test failed: ${error.message}`, 'error');
                
                const aiDiv = document.getElementById('ai-integration-results');
                aiDiv.innerHTML = `<div class="test-error"><h4>❌ AI Integration Test Failed</h4><p>${error.message}</p></div>`;
            }
            
            updateStatus();
        }

        function clearResults() {
            testResults = { questionTypes: {}, performanceData: {}, transmissionTest: null };
            document.getElementById('test-log').innerHTML = '<p>Test results cleared...</p>';
            document.getElementById('performance-data-display').innerHTML = '<p>Run tests to see collected performance data...</p>';
            document.getElementById('ai-integration-results').innerHTML = '<p>Test data transmission to see AI analysis integration status...</p>';
            
            // Reset all question type statuses
            document.querySelectorAll('.test-item').forEach(item => {
                const indicator = item.querySelector('.status-indicator');
                const status = item.querySelector('.status');
                indicator.className = 'status-indicator status-pending';
                status.textContent = 'Pending';
            });
            
            updateStatus();
            log('Test results cleared');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Performance data collection test initialized');
            updateStatus();
        });
    </script>
</body>
</html>
