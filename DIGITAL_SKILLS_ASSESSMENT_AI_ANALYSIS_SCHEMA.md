# Digital Skills Assessment AI Analysis Database Schema

## Overview
This document describes the enhanced database schema for the Digital Skills Assessment system with AI-powered analysis and reporting capabilities, following the same architecture patterns as the Mathematics Assessment system.

## Database Structure

### Collection: `companies/{companyId}/users/{userEmail}`

#### Core Digital Skills Assessment Fields

```javascript
{
  // Basic user information
  userEmail: "<EMAIL>",
  userCompany: "Birmingham",
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>", 
  name: "<PERSON>",
  studentLevel: "adult-learner",
  userType: "student",

  // Digital skills assessment completion status
  digitalSkillsAssessmentCompleted: true,
  digitalSkillsCurrentLevel: "Level1", // EntryLevel2, EntryLevel2Plus, EntryLevel3, Level1, Level2, Level3
  digitalSkillsOverallScore: 18,
  digitalSkillsAssessmentTimestamp: Timestamp,
  totalTimeSpentOnDigitalSkills: 1800, // seconds
  digitalSkillsHighestLevelCompleted: "Level1",
  
  // AI Analysis Results
  digitalSkillsSkillsLevel: "Level1", // AI-recommended skills level
  digitalSkillsFeedback: {
    basicComputerSkills: "Shows good understanding of fundamental computer operations...",
    internetAndEmail: "Demonstrates competent email and web browsing skills...",
    digitalSafety: "Awareness of basic security practices with room for improvement...",
    softwareApplications: "Comfortable with common productivity applications...",
    troubleshooting: "Developing problem-solving skills for technical issues...",
    overall: "Overall performance indicates readiness for Level 2 coursework..."
  },
  
  digitalSkillsStrengths: [
    "Strong foundation in basic computer operations",
    "Good understanding of file management",
    "Confident with email communication"
  ],
  
  digitalSkillsImprovements: [
    "Enhance digital safety awareness",
    "Develop advanced software skills", 
    "Improve troubleshooting techniques"
  ],
  
  digitalSkillsCourseRecommendation: {
    level: "Level2",
    title: "Computer Skills for Work / ICDL Level 2",
    reasoning: "Strong performance in foundational skills indicates readiness for workplace-focused training",
    nextSteps: [
      "Enroll in Level 2 course",
      "Practice advanced Microsoft Office features",
      "Focus on workplace digital communication"
    ]
  },
  
  digitalSkillsConfidenceAnalysis: {
    overallConfidence: "Medium", // High, Medium, Low
    confidenceAreas: [
      "Basic computer operations",
      "Email communication"
    ],
    developmentAreas: [
      "Advanced software features",
      "Digital security practices"
    ],
    practicalReadiness: "Ready for guided practice" // Ready for independent use, Ready for guided practice, Needs foundational support
  },

  // Timestamps
  updatedAt: Timestamp
}
```

#### Level-Specific Assessment Data

Each level stores detailed assessment data:

```javascript
{
  // Entry Level 2 (Computer Skills Beginners)
  digitalSkillsEntryLevel2: {
    completed: true,
    score: 16,
    passed: true,
    timeSpent: 1200,
    completedAt: Timestamp,
    responses: [
      {
        questionId: 1,
        answer: "Option A",
        timeSpent: 45
      }
      // ... more responses
    ],
    topicBreakdown: {
      computerBasics: { correct: 3, total: 4, percentage: 75 },
      mouseKeyboard: { correct: 2, total: 2, percentage: 100 },
      basicOperations: { correct: 4, total: 5, percentage: 80 }
    }
  },

  // Entry Level 2/3 (Computer Skills Beginners Plus)  
  digitalSkillsEntryLevel2Plus: {
    // Same structure as above
  },

  // Entry Level 3 (Improvers Plus)
  digitalSkillsEntryLevel3: {
    // Same structure as above
  },

  // Level 1 (Computer Skills for Everyday Life)
  digitalSkillsLevel1: {
    // Same structure as above
  },

  // Level 2 (Computer Skills for Work / ICDL Level 2)
  digitalSkillsLevel2: {
    // Same structure as above
  },

  // Level 3 (ICDL Level 3)
  digitalSkillsLevel3: {
    // Same structure as above
  }
}
```

#### Detailed Response Logging

```javascript
{
  digitalSkillsDetailedResponses: {
    assessmentMetadata: {
      startTime: Timestamp,
      endTime: Timestamp,
      userAgent: "Mozilla/5.0...",
      sessionId: "digital_1234567890_abc123",
      analysisTimestamp: Timestamp,
      aiAnalysisUsed: true
    },
    interactionLog: [
      {
        timestamp: Timestamp,
        action: "question_viewed",
        questionId: 1,
        data: { questionType: "multiple-choice" }
      },
      {
        timestamp: Timestamp,
        action: "answer_selected", 
        questionId: 1,
        data: { selectedAnswer: "Option B", previousAnswer: null }
      },
      {
        timestamp: Timestamp,
        action: "confidence_rating",
        questionId: 2,
        data: { rating: 7, scale: 10 }
      }
      // ... more interaction logs
    ]
  }
}
```

## API Endpoints

### Admin Dashboard Endpoints

#### GET `/api/admin/digital-skills-analytics`
Retrieve comprehensive analytics for digital skills assessments.

**Query Parameters:**
- `company` (optional): Company ID, defaults to "Birmingham"

**Response:**
```javascript
{
  success: true,
  data: {
    totalAssessments: 150,
    levelBreakdown: {
      EntryLevel2: { completed: 25, passed: 20, averageScore: 14 },
      EntryLevel2Plus: { completed: 30, passed: 25, averageScore: 16 },
      EntryLevel3: { completed: 35, passed: 28, averageScore: 18 },
      Level1: { completed: 25, passed: 22, averageScore: 20 },
      Level2: { completed: 20, passed: 18, averageScore: 22 },
      Level3: { completed: 15, passed: 12, averageScore: 24 }
    },
    overallPassRate: 78,
    averageTimeSpent: 1650,
    skillsLevelDistribution: {
      "EntryLevel2": 15,
      "EntryLevel3": 45,
      "Level1": 60,
      "Level2": 25,
      "Level3": 5
    },
    confidenceAnalysis: {
      highConfidence: 45,
      mediumConfidence: 85,
      lowConfidence: 20
    }
  }
}
```

#### GET `/api/admin/digital-skills-responses`
Retrieve recent digital skills assessment responses for admin review.

**Query Parameters:**
- `company` (optional): Company ID, defaults to "Birmingham"  
- `limit` (optional): Number of responses to return, defaults to 50

**Response:**
```javascript
{
  success: true,
  data: [
    {
      userEmail: "<EMAIL>",
      name: "John Doe",
      currentLevel: "Level1",
      overallScore: 18,
      skillsLevel: "Level2",
      completedAt: Timestamp,
      timeSpent: 1800,
      feedback: { /* AI feedback object */ },
      strengths: ["Strong foundation...", "Good understanding..."],
      improvements: ["Enhance digital safety...", "Develop advanced..."],
      courseRecommendation: { /* Course recommendation object */ },
      confidenceAnalysis: { /* Confidence analysis object */ }
    }
    // ... more responses
  ]
}
```

#### GET `/api/admin/digital-skills-responses/:email`
Retrieve detailed digital skills assessment data for a specific user.

**Query Parameters:**
- `company` (optional): Company ID, defaults to "Birmingham"

**Response:**
```javascript
{
  success: true,
  data: {
    userEmail: "<EMAIL>",
    name: "John Doe",
    assessmentCompleted: true,
    currentLevel: "Level1",
    overallScore: 18,
    skillsLevel: "Level2", 
    highestLevelCompleted: "Level1",
    completedAt: Timestamp,
    totalTimeSpent: 1800,
    feedback: { /* Complete AI feedback object */ },
    strengths: ["Strong foundation...", "Good understanding..."],
    improvements: ["Enhance digital safety...", "Develop advanced..."],
    courseRecommendation: { /* Complete course recommendation */ },
    confidenceAnalysis: { /* Complete confidence analysis */ },
    detailedResponses: { /* Complete interaction logs */ },
    // Level-specific data
    entryLevel2: { /* Level data if completed */ },
    entryLevel2Plus: { /* Level data if completed */ },
    entryLevel3: { /* Level data if completed */ },
    level1: { /* Level data if completed */ },
    level2: { /* Level data if completed */ },
    level3: { /* Level data if completed */ }
  }
}
```

## Digital Skills Level Progression Framework

### Course Levels and Descriptions

1. **Entry Level 2** - Computer Skills Beginners
   - Target: Absolute beginners with minimal computer experience
   - Focus: Basic computer operation, mouse/keyboard skills, turning on/off

2. **Entry Level 2/3** - Computer Skills Beginners Plus  
   - Target: Low-confidence users ready for some independence
   - Focus: File management, basic applications, guided practice

3. **Entry Level 3** - Improvers Plus
   - Target: Post-beginners with broader digital skills needs
   - Focus: Internet skills, email basics, digital safety awareness

4. **Level 1** - Computer Skills for Everyday Life
   - Target: Basic users seeking practical application confidence
   - Focus: Online services, digital communication, everyday tasks

5. **Level 2** - Computer Skills for Work / ICDL Level 2
   - Target: Job-focused office software skills
   - Focus: Microsoft Office proficiency, workplace digital skills

6. **Level 3** - ICDL Level 3
   - Target: Advanced qualification seekers pursuing IT careers
   - Focus: Advanced applications, professional certification preparation

## AI Analysis Integration

### Question Type Analysis

The AI analysis system processes three types of questions:

1. **Multiple-Choice Knowledge Questions (50%)**
   - Objective assessment of digital skills knowledge
   - Scored based on correct/incorrect answers
   - Contributes to topic breakdown analysis

2. **Interactive Self-Assessment Ratings (25%)**
   - Confidence-based scoring using rating scales
   - Provides insight into user self-perception
   - Influences confidence analysis

3. **Descriptive Confidence Assessments (25%)**
   - Text-based confidence level responses
   - Analyzed for practical readiness assessment
   - Informs course recommendation logic

### AI Prompt Engineering

The system uses specialized prompts that:
- Analyze mixed question types appropriately
- Consider both objective knowledge and subjective confidence
- Provide UK-focused educational recommendations
- Generate actionable feedback for learners
- Support 7-level progression framework

## Data Migration and Compatibility

This enhanced schema is designed to:
- Maintain backward compatibility with existing digital skills data
- Follow established patterns from mathematics assessment system
- Support seamless integration with admin dashboard
- Enable comprehensive reporting and analytics
- Facilitate future enhancements and extensions

## Security and Privacy

All assessment data follows the same security protocols as other assessment systems:
- User data encrypted in transit and at rest
- Access controlled through company-based collections
- Personal information handled according to data protection requirements
- Assessment responses logged for quality assurance and improvement
