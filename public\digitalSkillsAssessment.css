/**
 * Digital Skills Assessment Styling
 * Following mathInteractive.css patterns with blue theme
 * Colors: #1547bb (primary blue), #121c41 (dark blue)
 */

/* ============================================================================
   ASSESSMENT CONTAINER STYLES
   ============================================================================ */

#digital-skills-assessment-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Use the same background as the body/form - no override */
  background: none;
  position: relative;
  overflow: hidden;
}

#digital-skills-assessment-container .assessment-screen {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  max-height: 90vh;
  background: none !important;
  backdrop-filter: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
  overflow: hidden;
}

/* ============================================================================
   INSTRUCTION SCREEN STYLES
   ============================================================================ */

.instruction-container {
  background: #ffffff;
  border: 2px solid #1547bb;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(21, 71, 187, 0.15);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  text-align: center;
  position: relative;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.instruction-container:hover {
  box-shadow: 0 12px 40px rgba(21, 71, 187, 0.2);
  transform: translateY(-2px);
}

.instruction-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1547bb 0%, #121c41 100%);
}

.instruction-title {
  color: #121c41;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.instruction-content {
  margin-bottom: 2rem;
}

.instruction-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #1547bb;
  text-align: left;
  transition: all 0.3s ease;
}

.instruction-item:hover {
  background: #f0f4ff;
}

.instruction-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.instruction-item p {
  margin: 0;
  font-size: 1rem;
  color: #374151;
  text-align: left;
}

.instruction-actions {
  margin-top: 2rem;
}

.begin-btn {
  background: linear-gradient(135deg, #1547bb 0%, #0d3a8a 100%);
  color: #ffffff;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.3);
}

.begin-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(21, 71, 187, 0.4);
  background: linear-gradient(135deg, #0d3a8a 0%, #1547bb 100%);
}

.begin-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.3);
}

/* ============================================================================
   QUESTION SCREEN STYLES
   ============================================================================ */

.question-container {
  background: #ffffff;
  border: 2px solid #1547bb;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(21, 71, 187, 0.15);
  max-width: 800px;
  width: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.question-container:hover {
  box-shadow: 0 12px 40px rgba(21, 71, 187, 0.2);
  transform: translateY(-2px);
}

.question-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1547bb 0%, #121c41 100%);
}

.question-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f5f9;
}

.question-progress {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1547bb 0%, #0d3a8a 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 1px 2px rgba(21, 71, 187, 0.3);
}

.progress-text {
  color: #121c41;
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
}

.question-content {
  flex: 1;
  margin-bottom: 30px;
}

.question-title {
  color: #121c41;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 25px;
  line-height: 1.6;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.question-option {
  position: relative;
}

.question-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.option-label {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.option-label:hover {
  border-color: #1547bb;
  background: #f8faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.1);
}

.question-option input[type="radio"]:checked + .option-label {
  border-color: #1547bb;
  background: #e8f0ff;
  box-shadow: 0 6px 20px rgba(21, 71, 187, 0.15);
  transform: translateY(-1px);
}

.option-letter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #1547bb 0%, #0d3a8a 100%);
  color: #ffffff;
  border-radius: 50%;
  font-weight: 700;
  font-size: 1rem;
  flex-shrink: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(21, 71, 187, 0.3);
}

.question-option input[type="radio"]:checked + .option-label .option-letter {
  background: linear-gradient(135deg, #121c41 0%, #1547bb 100%);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(18, 28, 65, 0.4);
}

.option-label:hover .option-letter {
  transform: scale(1.05);
  box-shadow: 0 3px 8px rgba(21, 71, 187, 0.4);
}

.option-text {
  flex: 1;
  color: #374151;
  font-size: 1.05rem;
  line-height: 1.6;
  font-weight: 500;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  margin-top: auto;
}

.nav-btn, .submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.nav-btn {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.nav-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.submit-btn, .action-btn.primary {
  background: linear-gradient(135deg, #1547bb 0%, #0d3a8a 100%);
  color: #ffffff;
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.3);
}

.submit-btn:hover, .action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(21, 71, 187, 0.4);
}

/* ============================================================================
   RESULTS SCREEN STYLES
   ============================================================================ */

.results-container {
  background: #ffffff;
  border: 2px solid #1547bb;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(21, 71, 187, 0.15);
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.results-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1547bb 0%, #121c41 100%);
}

.results-header {
  margin-bottom: 30px;
}

.results-title {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.results-score {
  display: inline-flex;
  align-items: baseline;
  gap: 5px;
  padding: 20px 30px;
  border-radius: 12px;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.results-score.passed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
}

.results-score.needs-improvement {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
}

.score-total {
  font-size: 1.5rem;
  opacity: 0.8;
}

.results-content {
  text-align: left;
  margin-bottom: 30px;
}

.results-details-minimal {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.minimal-recommendation {
  margin-top: 20px;
  padding: 15px;
  background: #ffffff;
  border-radius: 8px;
  border-left: 4px solid #1547bb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.minimal-recommendation h4 {
  color: #000000;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.minimal-recommendation p {
  color: #374151;
  font-size: 0.9rem;
  margin: 0;
}

.results-summary {
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 25px;
  text-align: center;
}

.results-summary.passed {
  background: #ecfdf5;
  border: 2px solid #10b981;
}

.results-summary.needs-improvement {
  background: #fffbeb;
  border: 2px solid #f59e0b;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.status-text {
  color: #000000;
}

.results-feedback {
  color: #374151;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.results-details {
  margin-top: 25px;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.metric {
  background: #ffffff;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #1547bb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-label {
  display: block;
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.metric-value {
  display: block;
  color: #000000;
  font-size: 1.3rem;
  font-weight: 600;
}

.results-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.action-btn.secondary:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

/* ============================================================================
   ENHANCED LOADING SCREEN STYLES
   ============================================================================ */

/* Enhanced Loading Screen for Digital Skills Assessment */
.loading-container {
  padding: 3rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

.loading-animation {
  margin-bottom: 2rem;
}

/* Enhanced Spinner Animation */
.enhanced-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: rotate 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #1547bb;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #10b981;
  animation-delay: -0.5s;
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #f59e0b;
  animation-delay: -1s;
  width: 40%;
  height: 40%;
  top: 30%;
  left: 30%;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  color: #121c41;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.loading-text {
  color: #6b7280;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  font-weight: 500;
}

/* Progress Container */
.progress-container {
  margin-top: 2rem;
}

.progress-container .progress-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-container .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1547bb, #10b981, #f59e0b);
  border-radius: 3px;
}

/* Loading progress animation - only for loading screen */
#assessment-loading .progress-container .progress-fill.loading-animation {
  animation: progress-animation 3s ease-in-out infinite;
}

/* Assessment questions progress bar - no animation */
#assessment-questions .progress-fill {
  animation: none !important;
}

/* ============================================================================
   SELF-ASSESSMENT QUESTION STYLES
   ============================================================================ */

/* Self-assessment question container */
.self-assessment-question {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #3b82f6;
  border-radius: 16px;
  padding: 25px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.self-assessment-question .question-title {
  color: #1e40af;
  font-weight: 700;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.self-assessment-question .question-title::before {
  content: "🎯";
  font-size: 1.2em;
}

/* Rating scale container */
.rating-scale {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 20px 0;
}

.rating-scale-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.scale-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.scale-endpoints {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 5px;
}

/* Star rating system */
.star-rating {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin: 15px 0;
}

.star {
  font-size: 2rem;
  color: #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.star:hover,
.star.active {
  color: #fbbf24;
  transform: scale(1.1);
}

.star:hover {
  text-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

/* Slider rating system */
.slider-rating {
  margin: 20px 0;
}

.rating-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
}

.rating-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ffffff;
  border: 3px solid #1547bb;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.rating-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.4);
}

.rating-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ffffff;
  border: 3px solid #1547bb;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.slider-value {
  text-align: center;
  margin-top: 10px;
  font-weight: 600;
  color: #1547bb;
  font-size: 1.1rem;
}

/* Confidence level indicators */
.confidence-indicators {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
  font-size: 0.8rem;
  color: #6b7280;
}

.confidence-indicator {
  text-align: center;
  flex: 1;
}

.confidence-indicator.low {
  color: #ef4444;
}

.confidence-indicator.medium {
  color: #f59e0b;
}

.confidence-indicator.high {
  color: #10b981;
}

/* ============================================================================
   DESCRIPTIVE SELF-ASSESSMENT QUESTION STYLES
   ============================================================================ */

/* Descriptive self-assessment question container */
.descriptive-self-assessment-question {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: 16px;
  padding: 25px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
}

.descriptive-assessment-header {
  margin-bottom: 20px;
}

.confidence-prompt {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #0c4a6e;
  font-size: 1.1rem;
}

.confidence-icon {
  font-size: 1.3em;
}

.confidence-text {
  flex: 1;
}

/* Descriptive options container */
.descriptive-options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.descriptive-option {
  position: relative;
}

.descriptive-option input[type="radio"] {
  display: none;
}

.descriptive-option-label {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 18px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.descriptive-option-label:hover {
  border-color: #0ea5e9;
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
}

.descriptive-option-label.selected {
  border-color: #0ea5e9;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 6px 20px rgba(14, 165, 233, 0.25);
  transform: translateY(-1px);
}

.descriptive-option-label.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #0ea5e9 0%, #0284c7 100%);
}

/* Confidence indicator dot */
.confidence-indicator-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #cbd5e1;
  background: #ffffff;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.descriptive-option-label:hover .confidence-indicator-dot {
  border-color: #0ea5e9;
  background: #f0f9ff;
}

.descriptive-option-label.selected .confidence-indicator-dot {
  border-color: #0ea5e9;
  background: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.2);
}

/* Confidence level indicators */
.descriptive-option-label.confidence-high .confidence-indicator-dot {
  border-color: #10b981;
}

.descriptive-option-label.confidence-high.selected .confidence-indicator-dot {
  background: #10b981;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.descriptive-option-label.confidence-medium .confidence-indicator-dot {
  border-color: #f59e0b;
}

.descriptive-option-label.confidence-medium.selected .confidence-indicator-dot {
  background: #f59e0b;
  border-color: #f59e0b;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
}

.descriptive-option-label.confidence-low .confidence-indicator-dot {
  border-color: #ef4444;
}

.descriptive-option-label.confidence-low.selected .confidence-indicator-dot {
  background: #ef4444;
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Option content */
.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.confidence-level {
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.7;
}

.confidence-high .confidence-level {
  color: #10b981;
}

.confidence-medium .confidence-level {
  color: #f59e0b;
}

.confidence-low .confidence-level {
  color: #ef4444;
}

.option-description {
  font-size: 1rem;
  color: #374151;
  line-height: 1.4;
}

.descriptive-option-label.selected .option-description {
  color: #1f2937;
  font-weight: 500;
}

/* Selection indicator */
.selection-indicator {
  font-size: 1.2rem;
  color: #0ea5e9;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.descriptive-option-label.selected .selection-indicator {
  opacity: 1;
  transform: scale(1);
}

/* Rating scale options (1-5 buttons) */
.rating-option {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rating-option input[type="radio"] {
  display: none;
}

.rating-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  background: #ffffff;
  font-weight: 600;
  font-size: 1.2rem;
  color: #6b7280;
  transition: all 0.2s ease;
}

.rating-option:hover .rating-number {
  border-color: #1547bb;
  background: #f0f4ff;
  color: #1547bb;
  transform: scale(1.1);
}

.rating-option input[type="radio"]:checked + .rating-number {
  border-color: #1547bb;
  background: #1547bb;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.3);
}

/* Responsive adjustments for self-assessment questions */
@media (max-width: 768px) {
  .self-assessment-question {
    padding: 20px;
    margin: 15px 0;
  }

  .star {
    font-size: 1.5rem;
    gap: 6px;
  }

  .rating-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .rating-option {
    margin: 0 4px;
  }

  .rating-slider::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
  }

  /* Responsive adjustments for descriptive self-assessment questions */
  .descriptive-self-assessment-question {
    padding: 20px;
    margin: 15px 0;
  }

  .confidence-prompt {
    font-size: 1rem;
    gap: 10px;
  }

  .descriptive-option-label {
    padding: 15px 16px;
    gap: 12px;
  }

  .option-content {
    gap: 3px;
  }

  .confidence-level {
    font-size: 0.8rem;
  }

  .option-description {
    font-size: 0.95rem;
    line-height: 1.3;
  }

  .confidence-indicator-dot {
    width: 14px;
    height: 14px;
  }

  .selection-indicator {
    font-size: 1.1rem;
  }
}

@keyframes progress-animation {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #6b7280;
}

.step {
  position: relative;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.step.active {
  color: #1547bb;
  font-weight: 600;
  background-color: rgba(21, 71, 187, 0.1);
}

.step.completed {
  color: #10b981;
  font-weight: 500;
}

/* Smooth fade-in animation for loading screen */
#assessment-loading {
  animation: fadeIn 0.5s ease-in-out;
}

/* ============================================================================
   SMOOTH TRANSITIONS AND ANIMATIONS
   ============================================================================ */

/* Fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade-out animation */
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Smooth container transitions */
#digital-skills-assessment-container .assessment-screen {
  transition: opacity 0.4s ease, transform 0.4s ease;
}

#digital-skills-assessment-container .assessment-screen.fade-out {
  opacity: 0;
  transform: translateY(-20px);
}

#digital-skills-assessment-container .assessment-screen.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced question container animations */
.question-container {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Results container animation */
.results-container {
  animation: slideInUp 0.6s ease-out;
}

/* Button hover animations */
.nav-btn, .submit-btn, .action-btn {
  transition: all 0.3s ease;
}

.nav-btn:hover, .submit-btn:hover, .action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(21, 71, 187, 0.2);
}

/* ============================================================================
   DETAILED REPORT MODAL STYLES
   ============================================================================ */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
}

.detailed-report-modal {
  background: #ffffff;
  border-radius: 16px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal-overlay.show .detailed-report-modal {
  transform: scale(1);
}

.modal-header {
  background: linear-gradient(135deg, #1547bb 0%, #121c41 100%);
  color: #ffffff;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.modal-close {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: 30px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.detailed-analysis {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* ============================================================================
   PIE CHART STYLES
   ============================================================================ */

.topic-performance-chart {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.pie-chart-wrapper {
  position: relative;
  width: 300px;
  height: 300px;
}

.pie-chart {
  width: 100%;
  height: 100%;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 200px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  background: #f9fafb;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  flex-shrink: 0;
}

.legend-text {
  flex: 1;
}

.legend-label {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.legend-value {
  color: #6b7280;
  font-size: 0.75rem;
}

@media (max-width: 768px) {
  .chart-container {
    flex-direction: column;
    gap: 1.5rem;
  }

  .pie-chart-wrapper {
    width: 250px;
    height: 250px;
  }
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  #digital-skills-assessment-container {
    padding: 0.5rem;
  }

  #digital-skills-assessment-container .assessment-screen {
    padding: 15px;
    max-height: 95vh;
  }

  .instruction-container,
  .question-container,
  .results-container {
    padding: 1.5rem;
  }

  .instruction-title,
  .results-title {
    font-size: 1.25rem;
  }
  
  .question-title {
    font-size: 1.2rem;
  }
  
  .question-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .nav-btn,
  .submit-btn,
  .action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
  
  .results-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  #digital-skills-assessment-container {
    padding: 0.25rem;
  }

  #digital-skills-assessment-container .assessment-screen {
    max-height: 98vh;
  }

  .instruction-container,
  .question-container,
  .results-container {
    padding: 1rem;
  }

  .instruction-title,
  .results-title {
    font-size: 1.125rem;
    line-height: 1.2;
  }

  .instruction-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .instruction-item p {
    text-align: center;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .instruction-container,
  .question-container,
  .results-container {
    padding: 20px;
  }
  
  .instruction-title,
  .results-title {
    font-size: 1.8rem;
  }
  
  .results-score {
    font-size: 1.5rem;
    padding: 15px 20px;
  }
  
  .loading-content {
    padding: 30px 20px;
  }
}

/* ============================================================================
   TOPIC BREAKDOWN STYLES
   ============================================================================ */

.topic-breakdown {
  margin: 25px 0;
}

.breakdown-title {
  color: #121c41;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

.topic-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.topic-item {
  background: #f8f9ff;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s ease;
}

.topic-item.good {
  border-left-color: #10b981;
  background: #ecfdf5;
}

.topic-item.average {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.topic-item.needs-work {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.topic-name {
  color: #121c41;
  font-weight: 600;
  font-size: 1rem;
}

.topic-score {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
}

.topic-progress {
  margin-top: 8px;
}

.topic-progress .progress-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.topic-item.good .topic-progress .progress-fill {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.topic-item.average .topic-progress .progress-fill {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.topic-item.needs-work .topic-progress .progress-fill {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

/* ============================================================================
   RECOMMENDATIONS STYLES
   ============================================================================ */

.recommendations-section {
  margin: 20px 0;
  padding: 20px;
  border-radius: 12px;
  background: #f8f9ff;
  border-left: 4px solid #1547bb;
}

.recommendations-section.strengths {
  background: #ecfdf5;
  border-left-color: #10b981;
}

.recommendations-section.improvements {
  background: #fffbeb;
  border-left-color: #f59e0b;
}

.recommendations-section.recommendations {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.recommendations-section.next-steps {
  background: #f3e8ff;
  border-left-color: #8b5cf6;
}

.section-title {
  color: #121c41;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-list li {
  color: #374151;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.recommendation-list li:before {
  content: "•";
  color: #1547bb;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.recommendations-section.strengths .recommendation-list li:before {
  color: #10b981;
}

.recommendations-section.improvements .recommendation-list li:before {
  color: #f59e0b;
}

.recommendations-section.recommendations .recommendation-list li:before {
  color: #3b82f6;
}

.recommendations-section.next-steps .recommendation-list li:before {
  color: #8b5cf6;
}

/* ============================================================================
   TIMER WARNING STYLES
   ============================================================================ */

.timer-warning {
  color: #f59e0b !important;
  animation: pulse-warning 2s infinite;
}

.timer-critical {
  color: #ef4444 !important;
  animation: pulse-critical 1s infinite;
}

@keyframes pulse-warning {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes pulse-critical {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ============================================================================
   ELIGIBILITY CHECK STYLES
   ============================================================================ */

.eligibility-btn {
  width: 100%;
  padding: 12px 24px;
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.eligibility-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.eligibility-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Progress Summary Styles */
.progress-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-top: 16px;
}

.progress-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.progress-stat {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.progress-stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  display: block;
}

.progress-stat-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.progress-levels {
  margin-top: 16px;
}

.progress-level-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 8px;
}

.progress-level-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-level-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.progress-level-icon.completed {
  background: #10b981;
  color: white;
}

.progress-level-icon.failed {
  background: #ef4444;
  color: white;
}

.progress-level-icon.available {
  background: #2563eb;
  color: white;
}

.progress-level-icon.locked {
  background: #9ca3af;
  color: white;
}

.progress-level-details h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.progress-level-details p {
  margin: 2px 0 0 0;
  font-size: 12px;
  color: #64748b;
}

.progress-level-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.progress-level-status.completed {
  background: #dcfce7;
  color: #166534;
}

.progress-level-status.failed {
  background: #fef2f2;
  color: #991b1b;
}

.progress-level-status.available {
  background: #dbeafe;
  color: #1e40af;
}

.progress-level-status.locked {
  background: #f3f4f6;
  color: #6b7280;
}

.new-user-message {
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  color: #1e40af;
}

.new-user-message h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.new-user-message p {
  margin: 0;
  font-size: 14px;
}

.eligibility-note {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.eligibility-note p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
}

.level-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }

/* ============================================================================
   ACCESSIBILITY IMPROVEMENTS
   ============================================================================ */

.option-label:focus-within {
  outline: 2px solid #1547bb;
  outline-offset: 2px;
}

.nav-btn:focus,
.submit-btn:focus,
.action-btn:focus,
.begin-btn:focus {
  outline: 2px solid #1547bb;
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .option-label {
    border-width: 3px;
  }

  .question-option input[type="radio"]:checked + .option-label {
    border-width: 4px;
  }

  .topic-item {
    border-left-width: 6px;
  }
}

/* AI Skills Analysis Styles */
.skills-level-display {
  margin-top: 1.5rem;
}

.skills-level-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #374151;
}

.skills-level-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  background: linear-gradient(135deg, #1547bb 0%, #121c41 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.3);
}

.level-reasoning {
  margin-top: 0.75rem;
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
}

.ai-skills-analysis {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.title-icon {
  font-size: 1.5rem;
}

.feedback-sections {
  display: grid;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.feedback-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.feedback-item:hover {
  border-color: #1547bb;
  box-shadow: 0 2px 8px rgba(21, 71, 187, 0.1);
}

.feedback-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.feedback-icon {
  font-size: 1.25rem;
}

.feedback-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.feedback-text {
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.overall-feedback {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #3b82f6;
  border-radius: 8px;
  padding: 1rem;
}

.overall-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e40af;
  margin: 0 0 0.5rem 0;
}

.overall-text {
  color: #1e40af;
  margin: 0;
  font-weight: 500;
}

/* Confidence Analysis */
.confidence-analysis {
  background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
  border: 2px solid #d8b4fe;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.confidence-overview {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.confidence-level, .practical-readiness {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.confidence-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.areas-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
}

.areas-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.areas-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  padding-left: 1.5rem;
}

.areas-list li:last-child {
  border-bottom: none;
}

.areas-list.positive li::before {
  content: "✅";
  position: absolute;
  left: 0;
}

.areas-list.development li::before {
  content: "📈";
  position: absolute;
  left: 0;
}

/* Course Recommendations */
.course-recommendations {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.recommendation-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.course-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #0c4a6e;
  margin: 0 0 0.75rem 0;
}

.course-reasoning {
  color: #374151;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.steps-title {
  font-size: 1rem;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0 0 0.75rem 0;
}

.steps-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.steps-list li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: #374151;
}

.steps-list li::before {
  content: "🎯";
  position: absolute;
  left: 0;
}

/* Strengths and Improvements */
.strengths-improvements {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.strengths-section {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 2px solid #22c55e;
  border-radius: 12px;
  padding: 1.5rem;
}

.improvements-section {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 12px;
  padding: 1.5rem;
}

.section-title.positive {
  color: #15803d;
}

.section-title.development {
  color: #92400e;
}

.strengths-list, .improvements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.strength-item, .improvement-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  padding-left: 2rem;
  line-height: 1.5;
}

.strength-item:last-child, .improvement-item:last-child {
  border-bottom: none;
}

.strength-item::before {
  content: "⭐";
  position: absolute;
  left: 0;
  font-size: 1.25rem;
}

.improvement-item::before {
  content: "📈";
  position: absolute;
  left: 0;
  font-size: 1.25rem;
}

/* Responsive adjustments for new sections */
@media (max-width: 768px) {
  .strengths-improvements {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .confidence-overview {
    flex-direction: column;
    gap: 0.5rem;
  }

  .feedback-sections {
    gap: 0.75rem;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .title-icon {
    font-size: 1.25rem;
  }
}

/* ============================================================================
   PRACTICAL ASSESSMENT STYLES
   ============================================================================ */

.practical-assessment {
  width: 100%;
  margin: 1rem 0;
}

.practical-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.practical-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 60px;
}

.practical-option:hover {
  border-color: #1547bb;
  background: #f8faff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(21, 71, 187, 0.1);
}

.practical-option.selected {
  border-color: #1547bb;
  background: #f0f4ff;
  box-shadow: 0 4px 16px rgba(21, 71, 187, 0.15);
}

.practical-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.practical-option .option-text {
  flex: 1;
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
  line-height: 1.4;
  margin-left: 0.5rem;
}

.practical-option.selected .option-text {
  color: #1547bb;
  font-weight: 600;
}

.practical-option .selection-indicator {
  width: 24px;
  height: 24px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.practical-option.selected .selection-indicator {
  background: #1547bb;
  border-color: #1547bb;
  color: white;
}

/* Responsive design for practical options */
@media (max-width: 768px) {
  .practical-option {
    padding: 0.875rem;
    min-height: 56px;
  }

  .practical-option .option-text {
    font-size: 0.95rem;
  }

  .practical-options {
    gap: 0.625rem;
  }
}
