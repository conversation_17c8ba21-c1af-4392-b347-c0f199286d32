<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Questions Accessibility Test</title>
    <link rel="stylesheet" href="mathAssessment.css">
    <link rel="stylesheet" href="mathInteractive.css">
    <style>
        .accessibility-test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            background: #f8f9fa;
            border: 2px solid #000000;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-results {
            background: #d4edda;
            border: 2px solid #155724;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            color: #155724;
        }
        
        .test-error {
            background: #f8d7da;
            border: 2px solid #721c24;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            color: #721c24;
        }
        
        .contrast-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .contrast-item {
            border: 2px solid #000000;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .test-button {
            background: #000000;
            color: #ffffff;
            border: 3px solid #ffffff;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 700;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #333333;
        }
        
        .test-button:focus {
            outline: 3px solid #0066cc;
            outline-offset: 2px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-warning { background: #ffc107; }
        
        .accessibility-score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            border: 3px solid #000000;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .score-excellent { background: #d4edda; color: #155724; }
        .score-good { background: #d1ecf1; color: #0c5460; }
        .score-needs-work { background: #fff3cd; color: #856404; }
        .score-poor { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="accessibility-test-container">
        <h1>Interactive Questions Accessibility Test</h1>
        <p>This test validates WCAG compliance and visual accessibility across all interactive question types.</p>
        
        <div class="test-section">
            <h2>Test Controls</h2>
            <button class="test-button" onclick="runAllAccessibilityTests()">Run All Tests</button>
            <button class="test-button" onclick="testColorContrast()">Test Color Contrast</button>
            <button class="test-button" onclick="testKeyboardNavigation()">Test Keyboard Navigation</button>
            <button class="test-button" onclick="testScreenReaderCompatibility()">Test Screen Reader</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div class="test-section">
            <h2>Overall Accessibility Score</h2>
            <div id="accessibility-score" class="accessibility-score">
                Run tests to see accessibility score...
            </div>
        </div>
        
        <div class="test-section">
            <h2>Color Contrast Tests</h2>
            <div id="contrast-results">
                <p>Click "Test Color Contrast" to validate WCAG compliance...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Interactive Elements Test</h2>
            <div id="interactive-elements-test">
                <h3>Number Bonds Example</h3>
                <div class="number-bonds-container">
                    <div class="bonds-visual-container">
                        <div class="bond-equation">
                            <div class="bond-visual">8</div>
                            <div class="bond-operator">+</div>
                            <div class="bond-visual missing">
                                <div class="missing-placeholder">?</div>
                            </div>
                            <div class="bond-equals">=</div>
                            <div class="bond-result">15</div>
                        </div>
                    </div>
                    <div class="bonds-input-container">
                        <div class="bond-input-area">
                            <span class="bond-prompt">Enter the missing number:</span>
                            <input type="number" class="bond-input" placeholder="?">
                            <button class="check-btn">Check</button>
                        </div>
                    </div>
                </div>
                
                <h3>Number Line Slider Example</h3>
                <div class="number-line-container">
                    <div class="number-line-track">
                        <div class="number-line-handle" style="left: 50%;" tabindex="0"></div>
                    </div>
                    <div class="number-line-labels">
                        <span>0</span>
                        <span>5</span>
                        <span>10</span>
                    </div>
                    <div class="number-line-value">5</div>
                </div>
                <p style="color: #155724; font-weight: bold; margin-top: 10px;">
                    ✅ FIXED: Number line value indicator now has black background with white text (21:1 contrast ratio)
                </p>
                
                <h3>Visual Calculator Example</h3>
                <div class="calculator-container">
                    <div class="calculator-display">
                        <div class="calculation-steps">8 + 7</div>
                        <div class="current-display">15</div>
                    </div>
                    <div class="calculator-keypad">
                        <button class="calc-btn">7</button>
                        <button class="calc-btn">8</button>
                        <button class="calc-btn">9</button>
                        <button class="calc-btn operator">÷</button>
                        <button class="calc-btn">4</button>
                        <button class="calc-btn">5</button>
                        <button class="calc-btn">6</button>
                        <button class="calc-btn operator">×</button>
                        <button class="calc-btn">1</button>
                        <button class="calc-btn">2</button>
                        <button class="calc-btn">3</button>
                        <button class="calc-btn operator">-</button>
                        <button class="calc-btn">0</button>
                        <button class="calc-btn">.</button>
                        <button class="calc-btn equals">=</button>
                        <button class="calc-btn operator">+</button>
                    </div>
                </div>
                
                <h3>Drag & Drop Example</h3>
                <div class="matching-container">
                    <div class="draggable-items">
                        <div class="draggable-item" tabindex="0">2 + 3</div>
                        <div class="draggable-item" tabindex="0">4 × 2</div>
                        <div class="draggable-item" tabindex="0">10 ÷ 2</div>
                    </div>
                    <div class="drop-zones">
                        <div class="drop-zone">
                            <div class="drop-zone-label">Equals 5</div>
                        </div>
                        <div class="drop-zone">
                            <div class="drop-zone-label">Equals 8</div>
                        </div>
                        <div class="drop-zone">
                            <div class="drop-zone-label">Equals 5</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Keyboard Navigation Test</h2>
            <div id="keyboard-test-results">
                <p>Click "Test Keyboard Navigation" to validate tab order and focus states...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Screen Reader Compatibility</h2>
            <div id="screen-reader-results">
                <p>Click "Test Screen Reader" to validate ARIA labels and semantic structure...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test Results Log</h2>
            <div id="test-log">
                <p>Test results will appear here...</p>
            </div>
        </div>
    </div>

    <script>
        let accessibilityResults = {
            colorContrast: {},
            keyboardNavigation: {},
            screenReader: {},
            overallScore: 0
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type === 'error' ? 'test-error' : 'test-results';
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[Accessibility Test] ${message}`);
        }

        function calculateContrastRatio(color1, color2) {
            // Simplified contrast ratio calculation
            // In a real implementation, you'd use proper color parsing and luminance calculation
            const rgb1 = hexToRgb(color1);
            const rgb2 = hexToRgb(color2);
            
            if (!rgb1 || !rgb2) return 1;
            
            const l1 = getLuminance(rgb1);
            const l2 = getLuminance(rgb2);
            
            const lighter = Math.max(l1, l2);
            const darker = Math.min(l1, l2);
            
            return (lighter + 0.05) / (darker + 0.05);
        }

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        function getLuminance(rgb) {
            const rsRGB = rgb.r / 255;
            const gsRGB = rgb.g / 255;
            const bsRGB = rgb.b / 255;

            const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
            const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
            const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        }

        function testColorContrast() {
            log('Testing color contrast ratios...');
            
            const contrastTests = [
                { name: 'Number Bonds - Black text on white', fg: '#000000', bg: '#ffffff', minRatio: 4.5 },
                { name: 'Number Bonds - Missing placeholder', fg: '#dc3545', bg: '#f8f9fa', minRatio: 4.5 },
                { name: 'Calculator Display - White on black', fg: '#ffffff', bg: '#000000', minRatio: 4.5 },
                { name: 'Calculator Buttons - Black on white', fg: '#000000', bg: '#ffffff', minRatio: 4.5 },
                { name: 'Number Line Value - White on black (FIXED)', fg: '#ffffff', bg: '#000000', minRatio: 4.5 },
                { name: 'Number Line Labels - Black on white', fg: '#000000', bg: '#ffffff', minRatio: 4.5 },
                { name: 'Drag Items - Black on white', fg: '#000000', bg: '#ffffff', minRatio: 4.5 },
                { name: 'Drop Zones - Black on white', fg: '#000000', bg: '#ffffff', minRatio: 4.5 }
            ];
            
            let passedTests = 0;
            let totalTests = contrastTests.length;
            let resultsHTML = '<div class="contrast-test">';
            
            contrastTests.forEach(test => {
                const ratio = calculateContrastRatio(test.fg, test.bg);
                const passed = ratio >= test.minRatio;
                
                if (passed) passedTests++;
                
                resultsHTML += `
                    <div class="contrast-item" style="color: ${test.fg}; background: ${test.bg};">
                        <span class="status-indicator ${passed ? 'status-pass' : 'status-fail'}"></span>
                        <strong>${test.name}</strong><br>
                        Ratio: ${ratio.toFixed(2)}:1<br>
                        Required: ${test.minRatio}:1<br>
                        ${passed ? '✅ PASS' : '❌ FAIL'}
                    </div>
                `;
            });
            
            resultsHTML += '</div>';
            
            document.getElementById('contrast-results').innerHTML = resultsHTML;
            
            accessibilityResults.colorContrast = {
                passed: passedTests,
                total: totalTests,
                score: (passedTests / totalTests) * 100
            };
            
            log(`Color contrast test completed: ${passedTests}/${totalTests} passed (${Math.round((passedTests/totalTests)*100)}%)`);
            updateOverallScore();
        }

        function testKeyboardNavigation() {
            log('Testing keyboard navigation...');
            
            const focusableElements = document.querySelectorAll(
                'button, input, [tabindex]:not([tabindex="-1"])'
            );
            
            let accessibleElements = 0;
            let totalElements = focusableElements.length;
            
            focusableElements.forEach((element, index) => {
                // Test if element is focusable and has proper focus styles
                element.focus();
                const computedStyle = window.getComputedStyle(element, ':focus');
                const hasOutline = computedStyle.outline !== 'none' || 
                                 computedStyle.outlineWidth !== '0px' ||
                                 computedStyle.boxShadow !== 'none';
                
                if (hasOutline) {
                    accessibleElements++;
                }
            });
            
            accessibilityResults.keyboardNavigation = {
                passed: accessibleElements,
                total: totalElements,
                score: (accessibleElements / totalElements) * 100
            };
            
            document.getElementById('keyboard-test-results').innerHTML = `
                <div class="test-results">
                    <h4>Keyboard Navigation Results</h4>
                    <p><span class="status-indicator status-pass"></span>Focusable elements: ${totalElements}</p>
                    <p><span class="status-indicator ${accessibleElements === totalElements ? 'status-pass' : 'status-fail'}"></span>Elements with proper focus styles: ${accessibleElements}</p>
                    <p><strong>Score: ${Math.round((accessibleElements/totalElements)*100)}%</strong></p>
                </div>
            `;
            
            log(`Keyboard navigation test completed: ${accessibleElements}/${totalElements} elements accessible`);
            updateOverallScore();
        }

        function testScreenReaderCompatibility() {
            log('Testing screen reader compatibility...');
            
            const ariaTests = [
                { selector: 'input', attribute: 'aria-label', required: false },
                { selector: 'button', attribute: 'aria-label', required: false },
                { selector: '[role]', attribute: 'role', required: true },
                { selector: 'canvas', attribute: 'aria-label', required: true }
            ];
            
            let accessibleElements = 0;
            let totalElements = 0;
            
            // Test semantic HTML structure
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            const properHeadingStructure = headings.length > 0;
            
            // Test alt text for images (if any)
            const images = document.querySelectorAll('img');
            const imagesWithAlt = Array.from(images).filter(img => img.alt).length;
            
            // Test form labels
            const inputs = document.querySelectorAll('input');
            const inputsWithLabels = Array.from(inputs).filter(input => {
                return input.getAttribute('aria-label') || 
                       input.getAttribute('placeholder') ||
                       document.querySelector(`label[for="${input.id}"]`);
            }).length;
            
            totalElements = inputs.length + images.length + 1; // +1 for heading structure
            accessibleElements = inputsWithLabels + imagesWithAlt + (properHeadingStructure ? 1 : 0);
            
            accessibilityResults.screenReader = {
                passed: accessibleElements,
                total: totalElements,
                score: totalElements > 0 ? (accessibleElements / totalElements) * 100 : 100
            };
            
            document.getElementById('screen-reader-results').innerHTML = `
                <div class="test-results">
                    <h4>Screen Reader Compatibility Results</h4>
                    <p><span class="status-indicator ${properHeadingStructure ? 'status-pass' : 'status-fail'}"></span>Proper heading structure: ${properHeadingStructure ? 'Yes' : 'No'}</p>
                    <p><span class="status-indicator ${inputsWithLabels === inputs.length ? 'status-pass' : 'status-warning'}"></span>Form inputs with labels: ${inputsWithLabels}/${inputs.length}</p>
                    <p><span class="status-indicator ${images.length === 0 || imagesWithAlt === images.length ? 'status-pass' : 'status-fail'}"></span>Images with alt text: ${imagesWithAlt}/${images.length}</p>
                    <p><strong>Score: ${Math.round(accessibilityResults.screenReader.score)}%</strong></p>
                </div>
            `;
            
            log(`Screen reader compatibility test completed: ${Math.round(accessibilityResults.screenReader.score)}% accessible`);
            updateOverallScore();
        }

        function updateOverallScore() {
            const scores = [
                accessibilityResults.colorContrast.score || 0,
                accessibilityResults.keyboardNavigation.score || 0,
                accessibilityResults.screenReader.score || 0
            ];
            
            const validScores = scores.filter(score => score > 0);
            const overallScore = validScores.length > 0 ? 
                validScores.reduce((a, b) => a + b, 0) / validScores.length : 0;
            
            accessibilityResults.overallScore = overallScore;
            
            const scoreDiv = document.getElementById('accessibility-score');
            let scoreClass = 'score-poor';
            let scoreText = 'Needs Significant Improvement';
            
            if (overallScore >= 90) {
                scoreClass = 'score-excellent';
                scoreText = 'Excellent Accessibility';
            } else if (overallScore >= 75) {
                scoreClass = 'score-good';
                scoreText = 'Good Accessibility';
            } else if (overallScore >= 60) {
                scoreClass = 'score-needs-work';
                scoreText = 'Needs Improvement';
            }
            
            scoreDiv.className = `accessibility-score ${scoreClass}`;
            scoreDiv.innerHTML = `
                <div>Overall Accessibility Score</div>
                <div style="font-size: 36px; margin: 10px 0;">${Math.round(overallScore)}%</div>
                <div>${scoreText}</div>
            `;
        }

        function runAllAccessibilityTests() {
            log('Starting comprehensive accessibility test suite...');
            
            testColorContrast();
            setTimeout(() => testKeyboardNavigation(), 500);
            setTimeout(() => testScreenReaderCompatibility(), 1000);
            
            log('All accessibility tests completed!');
        }

        function clearResults() {
            accessibilityResults = {
                colorContrast: {},
                keyboardNavigation: {},
                screenReader: {},
                overallScore: 0
            };
            
            document.getElementById('contrast-results').innerHTML = '<p>Click "Test Color Contrast" to validate WCAG compliance...</p>';
            document.getElementById('keyboard-test-results').innerHTML = '<p>Click "Test Keyboard Navigation" to validate tab order and focus states...</p>';
            document.getElementById('screen-reader-results').innerHTML = '<p>Click "Test Screen Reader" to validate ARIA labels and semantic structure...</p>';
            document.getElementById('accessibility-score').innerHTML = 'Run tests to see accessibility score...';
            document.getElementById('accessibility-score').className = 'accessibility-score';
            document.getElementById('test-log').innerHTML = '<p>Test results cleared...</p>';
            
            log('All test results cleared');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Accessibility test suite initialized');
        });
    </script>
</body>
</html>
