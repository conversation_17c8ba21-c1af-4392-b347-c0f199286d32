# Standalone Mathematics Assessment Server (server2.js)

## Overview

This is a self-contained server focused specifically on mathematics assessment functionality, extracted from the main server for easier debugging and testing. It provides a complete mathematics assessment experience with all the features from the original implementation.

## Features

### Core Mathematics Assessment
- **4 Progressive Levels**: Entry Level, Level 1, GCSE Part 1, GCSE Part 2
- **AI-Powered Question Generation**: Uses OpenAI GPT-4o-mini for dynamic question creation
- **Interactive Question Types**: 
  - Multiple choice questions
  - Numeric input questions
  - Short answer questions
  - Interactive elements (number-line sliders, drag-and-drop matching, area models)
- **Comprehensive Scoring**: AI-powered assessment analysis with detailed feedback

### Performance Optimization
- **Intelligent Caching System**: In-memory cache with LRU eviction
- **Cache Warming**: Pre-populate cache on startup for better performance
- **Fallback System**: Enhanced fallback questions when AI generation fails
- **Performance Monitoring**: Real-time metrics and analytics

### Database Integration
- **Firebase Firestore**: Comprehensive result storage
- **Detailed Response Logging**: Captures all user interactions
- **Birmingham Company**: Auto-created for student users
- **Analytics Support**: Assessment statistics and performance tracking

## Quick Start

### Prerequisites
- Node.js (v14 or higher)
- Firebase service account key (`service_account.json`)
- OpenAI API key

### Installation
1. Ensure all dependencies are installed:
   ```bash
   npm install
   ```

2. Set up environment variables in `.env`:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   PORT=3001
   ```

3. Ensure `service_account.json` is in the root directory

### Running the Server
```bash
node server2.js
```

The server will start on port 3001 (or your specified PORT) to avoid conflicts with the main server.

## API Endpoints

### Assessment Endpoints
- `POST /api/math-assessments/start` - Start a new mathematics assessment
- `POST /api/math-assessments/submit/:id` - Submit assessment answers
- `GET /api/math-assessments/performance` - Get performance metrics

### Cache Management
- `POST /api/math-assessments/cache/clear` - Clear question cache
- `POST /api/math-assessments/cache/warm` - Pre-populate cache

### Analytics
- `GET /api/admin/math-analytics` - Get assessment analytics

### Utility
- `GET /` - Serve mathematics assessment page
- `GET /health` - Health check endpoint

## Configuration

### Assessment Levels
```javascript
'Entry': {
  count: 22,
  timeLimit: 30 * 60, // 30 minutes
  passingScore: 24,
  maxScore: 44
},
'Level1': {
  count: 13,
  timeLimit: 30 * 60, // 30 minutes
  passingScore: 16,
  maxScore: 26
},
'GCSEPart1': {
  count: 7,
  timeLimit: 15 * 60, // 15 minutes
  passingScore: 5,
  maxScore: 10
},
'GCSEPart2': {
  count: 10,
  timeLimit: 20 * 60, // 20 minutes
  passingScore: 8,
  maxScore: 20
}
```

### Cache Settings
```javascript
const CACHE_EXPIRY_TIME = 60 * 60 * 1000; // 1 hour
const CACHE_MAX_SIZE = 200;
const API_TIMEOUT_THRESHOLD = 35000; // 35 seconds
const PRELOAD_CACHE_ON_STARTUP = true;
```

## Testing

### Manual Testing
1. Start the server: `node server2.js`
2. Open browser to `http://localhost:3001`
3. Complete the mathematics assessment form
4. Take the assessment and verify results

### API Testing
Use the existing test file:
```bash
node test_math_api.js
```

### Performance Testing
Check performance metrics:
```bash
curl http://localhost:3001/api/math-assessments/performance
```

## Debugging

### Logging
The server provides comprehensive logging for:
- Question generation process
- Cache hits/misses
- AI API calls and responses
- Database operations
- Performance metrics

### Common Issues
1. **OpenAI API Key**: Ensure your API key is valid and has sufficient credits
2. **Firebase Connection**: Verify `service_account.json` is correct
3. **Port Conflicts**: Use a different port if 3001 is occupied
4. **Cache Issues**: Clear cache using the API endpoint if needed

## Differences from Main Server

This standalone server:
- **Focuses only on mathematics assessment** - No other assessment types
- **Uses port 3001** - Avoids conflicts with main server
- **Simplified structure** - Easier to debug and maintain
- **Self-contained** - All math-related functionality in one file
- **Enhanced logging** - Better debugging information

## File Structure

```
server2.js                 # Main standalone server file
public/
  ├── math.html            # Mathematics assessment frontend
  ├── mathAssessment.js    # Frontend JavaScript
  ├── style.css            # Styling
  └── ...                  # Other static assets
service_account.json       # Firebase credentials
.env                       # Environment variables
```

## Support

For issues or questions:
1. Check the console logs for detailed error information
2. Verify all prerequisites are met
3. Test with the health check endpoint
4. Review the performance metrics for insights

The standalone server maintains full compatibility with the original mathematics assessment functionality while providing a cleaner, more focused development environment.
