# Assessment Tool - Deployment Guide

## Render Deployment Configuration

This application is configured for deployment on Render with the following setup:

### Main Configuration
- **Entry Point**: `server2.js` (Mathematics Assessment Server)
- **Port**: Uses `process.env.PORT` (automatically provided by <PERSON><PERSON>)
- **Node.js Version**: >=18.0.0
- **Build Command**: `npm install` (no build step required)
- **Start Command**: `npm start` (runs `node server2.js`)

### Environment Variables Required
Make sure to set these environment variables in your Render dashboard:

1. **OPENAI_API_KEY** - Your OpenAI API key for assessment generation
2. **FIREBASE_PROJECT_ID** - Firebase project ID
3. **FIREBASE_PRIVATE_KEY** - Firebase private key (from service account)
4. **FIREBASE_CLIENT_EMAIL** - Firebase client email (from service account)
5. **SENDGRID_API_KEY** - SendGrid API key for email functionality

### Firebase Configuration
The application uses Firebase Admin SDK. Make sure your `service_account.json` file is properly configured or use environment variables for Firebase credentials.

### Features Included
- Mathematics Assessment System
- English Proficiency Assessment
- Digital Skills Assessment
- Interactive Question Types
- Performance Monitoring
- Admin Dashboard
- Student-focused UI

### Health Check
The server provides a health check endpoint at `/api/health` for monitoring.

### Performance Monitoring
Performance metrics are available at `/api/math-assessments/performance`.

## Local Development
```bash
npm install
npm run dev
```

## Production Deployment
The application is ready for production deployment on Render with:
- Optimized package-lock.json
- Security vulnerabilities fixed
- Proper environment variable handling
- Health check endpoints
- Performance monitoring
