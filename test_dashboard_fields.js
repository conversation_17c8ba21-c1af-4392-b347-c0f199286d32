// Test script to verify dashboard fields are stored correctly
const fetch = require('node-fetch');

async function testDashboardFields() {
  console.log('🧪 Testing Dashboard Fields Storage...\n');

  const testData = {
    answers: [
      {
        questionId: 1,
        questionType: "multiple-choice",
        topic: "arithmetic",
        studentAnswer: "62",
        timeSpent: 5000
      },
      {
        questionId: 2,
        questionType: "multiple-choice", 
        topic: "arithmetic",
        studentAnswer: "55",
        timeSpent: 4000
      }
    ],
    email: "<EMAIL>",
    level: "Entry",
    timeSpent: 600,
    userData: {
      firstName: "Test",
      lastName: "Dashboard",
      name: "Test Dashboard",
      studentLevel: "Beginner",
      userType: "student"
    }
  };

  try {
    // First, start an assessment
    console.log('1. Starting assessment...');
    const startResponse = await fetch('http://localhost:3001/api/math-assessments/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        level: testData.level,
        email: testData.email,
        studentLevel: testData.userData.studentLevel
      })
    });

    if (!startResponse.ok) {
      throw new Error(`Start assessment failed: ${startResponse.status}`);
    }

    const startResult = await startResponse.json();
    console.log('✅ Assessment started:', startResult.assessmentId);

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Submit the assessment
    console.log('\n2. Submitting assessment...');
    const submitResponse = await fetch(`http://localhost:3001/api/math-assessments/${startResult.assessmentId}/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    if (!submitResponse.ok) {
      const errorText = await submitResponse.text();
      throw new Error(`Submit assessment failed: ${submitResponse.status} - ${errorText}`);
    }

    const submitResult = await submitResponse.json();
    console.log('✅ Assessment submitted successfully');
    console.log('📊 Score:', submitResult.score);
    console.log('✅ Passed:', submitResult.passed);

    console.log('\n🎯 Dashboard Fields Test Results:');
    console.log('- User data should now be stored with required dashboard fields');
    console.log('- Email:', testData.email);
    console.log('- Name:', testData.userData.name);
    console.log('- User Type:', testData.userData.userType);
    console.log('- Student Level:', testData.userData.studentLevel);
    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testDashboardFields();
